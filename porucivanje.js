/**
 * Poručivanje.js - Logika za formu poručivanja
 * Collagen Honey - Order Form Handler
 */

// Funkcija za prikazivanje popup poruke
function showMessagePopup(type, title, message) {
    console.log('Showing popup:', type, title, message); // Debug log
    
    const popup = document.getElementById('messagePopup');
    const icon = document.getElementById('popupIcon');
    const titleEl = document.getElementById('popupTitle');
    const messageEl = document.getElementById('popupMessage');
    
    if (!popup || !icon || !titleEl || !messageEl) {
        console.error('Popup elements not found!');
        return;
    }
    
    // Postavi sadržaj
    titleEl.textContent = title;
    messageEl.textContent = message;
    
    // Postavi stil na osnovu tipa
    icon.className = `popup-icon ${type}`;
    titleEl.className = `popup-title ${type}`;
    
    // Prikaži popup
    popup.style.display = 'flex';
    setTimeout(() => {
        popup.classList.add('show');
    }, 10);
    document.body.style.overflow = 'hidden';
}

// Funkcija za zatvaranje popup-a
function closeMessagePopup() {
    console.log('Closing popup'); // Debug log
    
    const popup = document.getElementById('messagePopup');
    if (!popup) return;
    
    popup.classList.remove('show');
    
    setTimeout(() => {
        popup.style.display = 'none';
        document.body.style.overflow = '';
    }, 300);
    
    // Resetuj formu
    resetOrderForm();
    
    // Ukloni parametre iz URL-a
    clearUrlParameters();
}

// Funkcija za resetovanje forme
function resetOrderForm() {
    const form = document.querySelector('form[name="Porudžbina"]');
    if (form) {
        form.reset();
        
        // Resetuj broj kutija na 1
        const quantityInput = form.querySelector('input[name="Količina"]');
        if (quantityInput) {
            quantityInput.value = '1';
        }
        
        // Resetuj border boje svih polja
        const allInputs = form.querySelectorAll('input, select, textarea');
        allInputs.forEach(function(field) {
            field.style.borderColor = '';
        });
    }
}

// Funkcija za uklanjanje URL parametara
function clearUrlParameters() {
    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
}

// Funkcija za validaciju forme
function validateOrderForm(form) {
    const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    let firstInvalidField = null;

    // Resetuj sve border boje
    requiredFields.forEach(function(field) {
        field.style.borderColor = '';
    });

    // Proveri sva obavezna polja
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
            if (!firstInvalidField) {
                firstInvalidField = field;
            }
        }
    });

    // Proveri checkbox za potvrdu podataka
    const checkbox = form.querySelector('input[name="PotvrdaPodataka"]');
    if (checkbox && !checkbox.checked) {
        showMessagePopup('error', 'Greška', 'Morate potvrditi tačnost podataka.');
        return false;
    }

    // Ako ima grešaka, fokusiraj prvo neispravno polje
    if (!isValid) {
        if (firstInvalidField) {
            firstInvalidField.focus();
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        showMessagePopup('error', 'Greška', 'Molimo popunite sva obavezna polja.');
        return false;
    }

    return true;
}

// Funkcija za inicijalizaciju form handlera
function initializeOrderForm() {
    const form = document.querySelector('form[name="Porudžbina"]');
    const submitBtn = document.querySelector('.u-btn-submit');

    if (!form || !submitBtn) {
        console.warn('Order form or submit button not found');
        return;
    }

    // Ukloni postojeće event listenere
    submitBtn.onclick = null;

    // Dodaj naš event listener
    submitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Submit button clicked'); // Debug log

        // Validacija forme
        if (validateOrderForm(form)) {
            console.log('Form validation passed, submitting...'); // Debug log
            // Ako je validacija prošla, submituj formu
            form.submit();
        }
    });

    // Dodaj listener za Enter key na input poljima
    form.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            submitBtn.click();
        }
    });
}

// Funkcija za obradu URL parametara
function handleUrlParameters() {
    console.log('Checking URL parameters...'); // Debug log
    
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    const message = urlParams.get('msg');

    console.log('URL status:', status, 'message:', message); // Debug log

    if (status === 'success') {
        console.log('Success status detected, showing popup'); // Debug log
        const successText = message ? decodeURIComponent(message) : 'Vaša porudžbina je uspešno poslata i u procesu je obrade.';
        showMessagePopup('success', 'Uspešno!', successText);
    } else if (status === 'error') {
        console.log('Error status detected, showing popup'); // Debug log
        const errorText = message ? decodeURIComponent(message) : 'Došlo je do greške! Pokušajte ponovo.';
        showMessagePopup('error', 'Greška', errorText);
    }
}

// Funkcija za inicijalizaciju event listenera
function initializeEventListeners() {
    // Event listener za OK dugme
    const okBtn = document.getElementById('popupOkBtn');
    if (okBtn) {
        okBtn.addEventListener('click', closeMessagePopup);
    }

    // Zatvaranje popup-a klikom na overlay
    const popup = document.getElementById('messagePopup');
    if (popup) {
        popup.addEventListener('click', function(e) {
            if (e.target === this) {
                closeMessagePopup();
            }
        });
    }

    // Zatvaranje popup-a ESC tasterom
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const popup = document.getElementById('messagePopup');
            if (popup && popup.classList.contains('show')) {
                closeMessagePopup();
            }
        }
    });
}

// Glavna inicijalizacija - izvršava se odmah
(function() {
    console.log('Poručivanje.js loading...'); // Debug log
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    function initialize() {
        console.log('Poručivanje.js initialized'); // Debug log
        
        // Inicijalizuj sve komponente
        initializeOrderForm();
        initializeEventListeners();
        
        // Proveri URL parametre odmah
        handleUrlParameters();
        
        // Proveri ponovo nakon kratke pauze (za slučaj da se stranica još učitava)
        setTimeout(handleUrlParameters, 100);
    }
})();

// Export funkcija za globalno korišćenje
window.OrderFormHandler = {
    showMessage: showMessagePopup,
    closeMessage: closeMessagePopup,
    resetForm: resetOrderForm,
    validateForm: validateOrderForm
};
