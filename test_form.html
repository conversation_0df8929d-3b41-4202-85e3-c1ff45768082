<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Forma - Collagen Honey</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #d9a832;
            outline: none;
        }
        .required {
            color: #dc3545;
        }
        .submit-btn {
            background-color: #d9a832;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background-color: #c19627;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Test Forma - Collagen Honey Porudžbina</h1>
        
        <!-- <div class="info">
            <strong>Ova forma testira direktno PHP skriptu bez interferiranja postojećeg JavaScript koda.</strong>
        </div> -->

        <div class="success-message" id="successMessage">
            Vaša porudžbina je uspešno poslata i u procesu je obrade.
            <div style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                <span id="successCountdown"></span>
            </div>
        </div>

        <div class="error-message" id="errorMessage">
            Došlo je do greške! Pokušajte ponovo.
            <div style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                <span id="errorCountdown"></span>
            </div>
        </div>

        <form action="process_order.php" method="POST" id="testForm">
            <div class="form-group">
                <label for="ime">Ime i prezime <span class="required">*</span></label>
                <input type="text" id="ime" name="Poručuje" required>
            </div>

            <div class="form-group">
                <label for="adresa">Ulica i broj <span class="required">*</span></label>
                <input type="text" id="adresa" name="Adresa" required>
            </div>

            <div class="form-group">
                <label for="grad">Grad <span class="required">*</span></label>
                <input type="text" id="grad" name="Grad" required>
            </div>

            <div class="form-group">
                <label for="postanski">Poštanski broj <span class="required">*</span></label>
                <input type="text" id="postanski" name="PoštanskiBroj" required>
            </div>

            <div class="form-group">
                <label for="telefon">Kontakt telefon <span class="required">*</span></label>
                <input type="tel" id="telefon" name="Telefon" required>
            </div>

            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="Email">
            </div>

            <div class="form-group">
                <label for="napomena">Napomena</label>
                <textarea id="napomena" name="textarea" rows="4"></textarea>
            </div>

            <div class="form-group">
                <label for="kolicina">Broj kutija <span class="required">*</span></label>
                <input type="number" id="kolicina" name="Količina" value="1" min="1" required>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="potvrda" name="PotvrdaPodataka" required>
                    <label for="potvrda">Potvrđujem tačnost podataka <span class="required">*</span></label>
                </div>
            </div>

            <button type="submit" class="submit-btn">PORUČI</button>
        </form>
    </div>

    <script>
        // Funkcija za countdown timer
        function startCountdown(elementId, seconds, callback) {
            const element = document.getElementById(elementId);
            if (!element) return;

            let remaining = seconds;

            function updateCountdown() {
                element.textContent = `Poruka će se sakriti za ${remaining} sekundi...`;
                remaining--;

                if (remaining < 0) {
                    clearInterval(timer);
                    if (callback) callback();
                }
            }

            updateCountdown();
            const timer = setInterval(updateCountdown, 1000);
        }

        // Proveri URL parametre za status
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            const message = urlParams.get('msg');
            
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');
            
            if (status === 'success') {
                // Prikaži success poruku
                successDiv.style.display = 'block';
                successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Sakrij dugme za poručivanje
                const submitBtn = document.querySelector('.submit-btn');
                if (submitBtn) {
                    submitBtn.style.display = 'none';
                }

                // Pokreni countdown timer
                startCountdown('successCountdown', 10, function() {
                    successDiv.style.display = 'none';
                    if (submitBtn) {
                        submitBtn.style.display = 'block';
                    }

                    // Ukloni parametre iz URL-a
                    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                });
            } else if (status === 'error') {
                // Ažuriraj sadržaj error poruke, ali zadrži countdown element
                const errorText = message ? decodeURIComponent(message) : 'Došlo je do greške! Pokušajte ponovo.';
                const countdownElement = errorDiv.querySelector('#errorCountdown');

                if (countdownElement) {
                    // Zameni samo tekst, zadrži countdown
                    errorDiv.innerHTML = errorText + '<div style="margin-top: 10px; font-size: 14px; opacity: 0.8;"><span id="errorCountdown"></span></div>';
                } else {
                    errorDiv.textContent = errorText;
                }

                // Prikaži error poruku
                errorDiv.style.display = 'block';
                errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Sakrij dugme za poručivanje
                const submitBtn = document.querySelector('.submit-btn');
                if (submitBtn) {
                    submitBtn.style.display = 'none';
                }

                // Pokreni countdown timer
                startCountdown('errorCountdown', 10, function() {
                    errorDiv.style.display = 'none';
                    if (submitBtn) {
                        submitBtn.style.display = 'block';
                    }

                    // Ukloni parametre iz URL-a
                    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                });
            }

            // Dodaj validaciju na submit
            const form = document.getElementById('testForm');
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.style.borderColor = '#dc3545';
                        isValid = false;
                    } else {
                        field.style.borderColor = '#ddd';
                    }
                });
                
                const checkbox = form.querySelector('input[name="PotvrdaPodataka"]');
                if (checkbox && !checkbox.checked) {
                    alert('Morate potvrditi tačnost podataka.');
                    isValid = false;
                }
                
                if (!isValid) {
                    e.preventDefault();
                    alert('Molimo popunite sva obavezna polja.');
                }
            });
        });
    </script>
</body>
</html>
