<?php
/**
 * Pregled porudžbina iz log fajla
 * Otvorite ovaj fajl u browseru da vidite sve porudžbine
 */

require_once 'email_config.php';

// Proveri da li log fajl postoji
if (!file_exists(LOG_FILE)) {
    die('Log fajl ne postoji. Još nema porudžbina.');
}

// Učitaj sadržaj log fajla
$log_content = file_get_contents(LOG_FILE);

if (empty($log_content)) {
    die('Log fajl je prazan. Još nema porudžbina.');
}

// Podeli log na pojedinačne porudžbine
$orders = explode('=== NOVA PORUDŽBINA ===', $log_content);
array_shift($orders); // Ukloni prvi prazan element

?>
<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregled Porudžbina - Collagen Honey</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #d9a832;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .order {
            background: white;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .order-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }
        .order-content {
            padding: 20px;
        }
        .order-field {
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            min-width: 150px;
            margin-right: 10px;
        }
        .field-value {
            color: #6c757d;
            flex: 1;
        }
        .total-price {
            background: #d9a832;
            color: white;
            padding: 10px 20px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .stats {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #d9a832;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 Pregled Porudžbina - Collagen Honey</h1>
        <p>Ukupno porudžbina: <?php echo count($orders); ?></p>
    </div>

    <?php
    // Izračunaj statistike
    $total_orders = count($orders);
    $total_boxes = 0;
    $total_revenue = 0;
    $customers_with_email = 0;
    
    foreach ($orders as $order) {
        // Izvuci broj kutija
        if (preg_match('/Broj kutija: (\d+)/', $order, $matches)) {
            $total_boxes += intval($matches[1]);
        }
        
        // Izvuci ukupnu cenu
        if (preg_match('/UKUPNA CENA: (\d+) RSD/', $order, $matches)) {
            $total_revenue += intval($matches[1]);
        }
        
        // Proveri da li ima email
        if (strpos($order, 'Email:') !== false && !preg_match('/Email:\s*$/', $order)) {
            $customers_with_email++;
        }
    }
    ?>

    <div class="stats">
        <h2>📊 Statistike</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number"><?php echo $total_orders; ?></div>
                <div class="stat-label">Ukupno porudžbina</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $total_boxes; ?></div>
                <div class="stat-label">Ukupno kutija</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo number_format($total_revenue, 0, ',', '.'); ?> RSD</div>
                <div class="stat-label">Ukupan prihod</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $customers_with_email; ?></div>
                <div class="stat-label">Sa email adresom</div>
            </div>
        </div>
    </div>

    <div class="search-box">
        <input type="text" class="search-input" placeholder="🔍 Pretražite porudžbine (ime, telefon, email, grad...)" id="searchInput">
    </div>

    <div id="ordersContainer">
        <?php
        $order_number = $total_orders;
        foreach ($orders as $order) {
            echo '<div class="order" data-order="' . htmlspecialchars($order) . '">';
            echo '<div class="order-header">Porudžbina #' . $order_number . '</div>';
            echo '<div class="order-content">';
            
            // Parsiraj porudžbinu
            $lines = explode("\n", trim($order));
            $total_price = '';
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || $line === '========================') continue;
                
                if (strpos($line, 'UKUPNA CENA:') !== false) {
                    $total_price = $line;
                    continue;
                }
                
                if (strpos($line, ':') !== false) {
                    list($label, $value) = explode(':', $line, 2);
                    $label = trim($label);
                    $value = trim($value);
                    
                    if (!empty($value)) {
                        echo '<div class="order-field">';
                        echo '<div class="field-label">' . htmlspecialchars($label) . ':</div>';
                        echo '<div class="field-value">' . htmlspecialchars($value) . '</div>';
                        echo '</div>';
                    }
                }
            }
            
            echo '</div>';
            
            if (!empty($total_price)) {
                echo '<div class="total-price">' . htmlspecialchars($total_price) . '</div>';
            }
            
            echo '</div>';
            $order_number--;
        }
        ?>
    </div>

    <script>
        // Funkcija za pretragu
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const orders = document.querySelectorAll('.order');
            
            orders.forEach(function(order) {
                const orderData = order.getAttribute('data-order').toLowerCase();
                if (orderData.includes(searchTerm)) {
                    order.classList.remove('hidden');
                } else {
                    order.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
