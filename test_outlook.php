<?php
/**
 * Test skripta za proveru dostave emailova na Outlook
 */

require_once 'email_config.php';

$test_email = isset($_POST['test_email']) ? trim($_POST['test_email']) : '';
$test_sent = false;
$test_result = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($test_email)) {
    // Kreiraj test email
    $subject = "Test Email - Collagen Honey";
    $message = "
    <html>
    <head>
        <title>Test Email</title>
        <style>
            body { font-family: Arial, sans-serif; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #d9a832; color: white; padding: 15px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .success { color: #28a745; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Test Email - Collagen Honey</h2>
            </div>
            <div class='content'>
                <p class='success'>✅ Uspešno! Ovaj email je stigao na vašu Outlook adresu.</p>
                <p><strong>Vreme slanja:</strong> " . date('d.m.Y H:i:s') . "</p>
                <p><strong>Test email adresa:</strong> $test_email</p>
                <p><strong>Server:</strong> " . $_SERVER['SERVER_NAME'] . "</p>
                <p><strong>IP adresa:</strong> " . $_SERVER['SERVER_ADDR'] . "</p>
                
                <h3>Šta ovo znači?</h3>
                <ul>
                    <li>Email sistem radi ispravno</li>
                    <li>Outlook prima emailove sa vašeg servera</li>
                    <li>Porudžbine će stizati na email</li>
                </ul>
                
                <p><em>Ako vidite ovaj email, sistem je spreman za produkciju!</em></p>
            </div>
        </div>
    </body>
    </html>";
    
    // Email headers optimizovani za Outlook
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8" . "\r\n";
    $headers .= "Content-Transfer-Encoding: 8bit" . "\r\n";
    $headers .= "From: Collagen Honey <" . FROM_EMAIL . ">" . "\r\n";
    $headers .= "Reply-To: Collagen Honey <" . REPLY_TO_EMAIL . ">" . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $headers .= "X-Priority: 3" . "\r\n";
    $headers .= "X-MSMail-Priority: Normal" . "\r\n";
    $headers .= "Importance: Normal" . "\r\n";
    
    // Pošalji email
    if (mail($test_email, $subject, $message, $headers)) {
        $test_result = 'success';
        $test_sent = true;
    } else {
        $test_result = 'error';
    }
}

?>
<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Outlook Dostave - Collagen Honey</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #d9a832;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #d9a832;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        .btn:hover {
            background: #c19627;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .checklist {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .checklist ul {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📧 Test Outlook Dostave</h1>
        <p>Testirajte da li emailovi stižu na Outlook adrese</p>
    </div>

    <div class="container">
        <?php if ($test_sent && $test_result === 'success'): ?>
            <div class="success">
                <h3>✅ Email je poslat!</h3>
                <p>Test email je uspešno poslat na <strong><?php echo htmlspecialchars($test_email); ?></strong></p>
                <p><strong>Sledeći koraci:</strong></p>
                <ol>
                    <li>Proverite inbox na Outlook adresi</li>
                    <li>Proverite spam/junk folder</li>
                    <li>Ako email nije stigao, proverite server log</li>
                </ol>
            </div>
        <?php elseif ($test_sent && $test_result === 'error'): ?>
            <div class="error">
                <h3>❌ Greška pri slanju</h3>
                <p>Email nije mogao da bude poslat. Mogući uzroci:</p>
                <ul>
                    <li>Server ne podržava mail() funkciju</li>
                    <li>Neispravna email konfiguracija</li>
                    <li>Potrebna je SMTP konfiguracija</li>
                </ul>
            </div>
        <?php endif; ?>

        <div class="info">
            <h3>📋 Informacije o serveru</h3>
            <p><strong>PHP verzija:</strong> <?php echo phpversion(); ?></p>
            <p><strong>Mail funkcija:</strong> <?php echo function_exists('mail') ? '✅ Dostupna' : '❌ Nije dostupna'; ?></p>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_NAME'] ?? 'N/A'; ?></p>
            <p><strong>FROM_EMAIL:</strong> <?php echo FROM_EMAIL; ?></p>
            <p><strong>TO_EMAIL:</strong> <?php echo TO_EMAIL; ?></p>
        </div>

        <form method="POST">
            <div class="form-group">
                <label for="test_email">Unesite vašu Outlook email adresu:</label>
                <input type="email" id="test_email" name="test_email" 
                       placeholder="<EMAIL>" 
                       value="<?php echo htmlspecialchars($test_email); ?>" required>
            </div>
            <button type="submit" class="btn">📧 Pošalji test email</button>
        </form>

        <div class="checklist">
            <h3>✅ Checklist za Outlook dostavu</h3>
            <ul>
                <li>✅ HTML email format - implementiran</li>
                <li>✅ Pravilni email headers - implementiran</li>
                <li>✅ UTF-8 encoding - implementiran</li>
                <li>✅ Inline CSS stilovi - implementiran</li>
                <li>✅ X-MSMail-Priority header - implementiran</li>
                <li>⚠️ SPF zapis - zavisi od hosting provajdera</li>
                <li>⚠️ DKIM potpis - zavisi od hosting provajdera</li>
                <li>⚠️ Reputation IP adrese - zavisi od servera</li>
            </ul>
        </div>

        <div class="info">
            <h3>💡 Saveti za bolju dostavu na Outlook</h3>
            <ul>
                <li><strong>Koristite SMTP:</strong> Pouzdaniji od PHP mail() funkcije</li>
                <li><strong>Proverite spam score:</strong> Koristite alate kao što je Mail Tester</li>
                <li><strong>Dodajte SPF zapis:</strong> Kontaktirajte hosting provajdera</li>
                <li><strong>Koristite poznati domen:</strong> Izbegavajte besplatne email servise</li>
                <li><strong>Testirajte redovno:</strong> Outlook često menja spam filtere</li>
            </ul>
        </div>

        <?php if (USE_SMTP): ?>
            <div class="info">
                <h3>📡 SMTP konfiguracija je aktivna</h3>
                <p>Koristite SMTP umesto PHP mail() funkcije što je bolje za Outlook dostavu.</p>
            </div>
        <?php else: ?>
            <div class="info">
                <h3>⚙️ Preporučujemo SMTP</h3>
                <p>Za bolju dostavu na Outlook, razmislite o aktiviranju SMTP konfiguracije u <code>email_config.php</code></p>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
