.u-section-1 .u-sheet-1 {
  min-height: 799px;
}

.u-section-1 .u-shape-1 {
  width: 950px;
  height: 628px;
  background-image: none;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 0 190px 0 0;
}

.u-section-1 .u-layout-wrap-1 {
  width: 1002px;
  margin: -580px 50px 48px auto;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 703px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0 40px 30px 20px;
}

.u-section-1 .u-text-1 {
  font-size: 2.25rem;
  text-transform: uppercase;
  font-weight: 700;
  margin: 30px 0 0;
}

.u-section-1 .u-text-2 {
  font-size: 1.25rem;
  margin: 16px 0 0;
}

.u-section-1 .u-btn-1 {
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 1px;
  --radius: 25px;
  background-image: none;
  margin: 30px auto 0;
}

.u-section-1 .u-image-1 {
  min-height: 703px;
  background-image: url("images/faq-img.jpg");
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 756px;
  }

  .u-section-1 .u-shape-1 {
    width: 813px;
    margin-right: 0;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 904px;
    margin-right: auto;
    margin-left: 0;
  }

  .u-section-1 .u-layout-cell-1 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 21deg;
    --animation-custom_in-scale: 1;
    min-height: 660px;
  }

  .u-section-1 .u-btn-1 {
    border-style: solid;
    padding: 12px 19px 13px 17px;
  }

  .u-section-1 .u-image-1 {
    background-position: 25.82% 50%;
    min-height: 660px;
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: -300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 872px;
  }

  .u-section-1 .u-shape-1 {
    height: 698px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 660px;
    margin-top: -698px;
    margin-bottom: 36px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-right: 30px;
    padding-left: 40px;
  }

  .u-section-1 .u-btn-1 {
    padding: 10px 30px;
  }

  .u-section-1 .u-image-1 {
    min-height: 506px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 422px;
  }

  .u-section-1 .u-shape-1 {
    height: 422px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 420px;
    margin-top: -422px;
    margin-left: auto;
    margin-bottom: 0;
  }

  .u-section-1 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 50px;
  }

  .u-section-1 .u-image-1 {
    min-height: 593px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 527px;
  }

  .u-section-1 .u-shape-1 {
    height: 485px;
    margin-top: 21px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 287px;
    margin-top: -485px;
    margin-bottom: 38px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 421px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 30px;
  }

  .u-section-1 .u-text-1 {
    font-size: 1.5rem;
  }

  .u-section-1 .u-text-2 {
    width: auto;
    margin-top: 60px;
  }

  .u-section-1 .u-image-1 {
    background-position: 97.91% 50%;
    min-height: 478px;
  }
} .u-section-2 {
  min-height: 633px;
}

.u-section-2 .u-text-1 {
  font-size: 2.25rem;
  font-weight: 700;
  text-transform: uppercase;
  margin: 30px 206px 0;
}

.u-section-2 .u-shape-1 {
  height: 359px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 123px 0 0;
}

.u-section-2 .u-list-1 {
  width: 751px;
  margin: -452px auto 30px;
}

.u-section-2 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 15px));
  min-height: 503px;
  grid-auto-columns: calc(50% - 15px);
  grid-gap: 30px;
}

.u-section-2 .u-list-item-1 {
  transition-duration: 0.5s;
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  --radius: 20px;
}

.u-section-2 .u-container-layout-1 {
  padding: 20px;
}

.u-section-2 .u-text-2 {
  font-weight: 700;
  font-size: 1.25rem;
  text-transform: none;
  margin: 0;
}

.u-section-2 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-2 {
  transition-duration: 0.5s;
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  --radius: 20px;
}

.u-section-2 .u-container-layout-2 {
  padding: 20px;
}

.u-section-2 .u-text-4 {
  font-weight: 700;
  font-size: 1.25rem;
  text-transform: none;
  margin: 0;
}

.u-section-2 .u-text-5 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-3 {
  transition-duration: 0.5s;
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  --radius: 20px;
}

.u-section-2 .u-container-layout-3 {
  padding: 20px;
}

.u-section-2 .u-text-6 {
  font-weight: 700;
  font-size: 1.25rem;
  text-transform: none;
  margin: 0;
}

.u-section-2 .u-text-7 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-4 {
  transition-duration: 0.5s;
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  --radius: 20px;
}

.u-section-2 .u-container-layout-4 {
  padding: 20px;
}

.u-section-2 .u-text-8 {
  font-weight: 700;
  font-size: 1.25rem;
  text-transform: none;
  margin: 0;
}

.u-section-2 .u-text-9 {
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
   .u-section-2 {
    min-height: 790px;
  }

  .u-section-2 .u-text-1 {
    margin-top: 31px;
    margin-left: 106px;
    margin-right: 106px;
  }

  .u-section-2 .u-shape-1 {
    animation-duration: 1500ms;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-2 .u-list-1 {
    width: 760px;
    margin-right: calc(((100% - 940px) / 2)  + 82px);
    margin-bottom: 31px;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 659px;
  }

  .u-section-2 .u-list-item-1 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-2 .u-list-item-2 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-2 .u-list-item-3 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-2 .u-list-item-4 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-text-1 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-list-1 {
    width: 720px;
    margin-right: calc(((100% - 720px) / 2));
    margin-left: calc(((100% - 720px) / 2));
  }

  .u-section-2 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
    min-height: 503px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-list-1 {
    width: 540px;
    margin-right: calc(((100% - 540px) / 2));
    margin-left: calc(((100% - 540px) / 2));
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-2 .u-container-layout-3 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-text-1 {
    font-size: 1.5rem;
  }

  .u-section-2 .u-list-1 {
    width: 340px;
    margin-right: calc(((100% - 340px) / 2));
    margin-left: calc(((100% - 340px) / 2));
  }

  .u-section-2 .u-repeater-1 {
    min-height: 906px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-2 .u-text-2 {
    width: auto;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-2 .u-text-4 {
    width: auto;
  }

  .u-section-2 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-2 .u-text-6 {
    width: auto;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-2 .u-text-8 {
    width: auto;
  }
} .u-section-3 {
  min-height: 50vh;
}

.u-section-3 .u-sheet-1 {
  min-height: 50vh;
}

.u-section-3 .u-list-1 {
  width: 711px;
  margin: 60px auto;
}

.u-section-3 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 15px));
  min-height: 363px;
  grid-auto-columns: calc(50% - 15px);
  grid-gap: 30px;
}

.u-section-3 .u-list-item-1 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-3 .u-container-layout-1 {
  padding: 20px;
}

.u-section-3 .u-text-1 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 20px;
}

.u-section-3 .u-text-2 {
  margin: 0 20px;
}

.u-section-3 .u-list-item-2 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-3 .u-container-layout-2 {
  padding: 20px;
}

.u-section-3 .u-text-3 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 20px;
}

.u-section-3 .u-text-4 {
  margin: 0 20px;
}

@media (max-width: 991px) {
  .u-section-3 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-list-1 {
    width: 540px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-1 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-text-2 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-text-4 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-list-1 {
    width: 340px;
  }
} .u-section-4 {
  min-height: 935px;
}

.u-section-4 .u-shape-1 {
  height: 543px;
  animation-duration: 1500ms;
  margin-top: 422px;
  margin-bottom: 0;
}

.u-section-4 .u-list-1 {
  width: 710px;
  margin: -930px auto 36px;
}

.u-section-4 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 15px));
  min-height: 865px;
  grid-auto-columns: calc(50% - 15px);
  grid-gap: 30px;
}

.u-section-4 .u-list-item-1 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-4 .u-container-layout-1 {
  padding: 20px;
}

.u-section-4 .u-text-1 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-2 {
  margin: 3px 0 0;
}

.u-section-4 .u-list-item-2 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-4 .u-container-layout-2 {
  padding: 20px;
}

.u-section-4 .u-text-3 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-4 {
  margin: 3px 0 0;
}

.u-section-4 .u-list-item-3 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-4 .u-container-layout-3 {
  padding: 20px;
}

.u-section-4 .u-text-5 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-6 {
  margin: 3px 0 0;
}

.u-section-4 .u-list-item-4 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-4 .u-container-layout-4 {
  padding: 20px;
}

.u-section-4 .u-text-7 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-8 {
  margin: 3px 0 0;
}

.u-section-4 .u-list-item-5 {
  box-shadow: 5px 5px 20px 0px rgba(0,0,0,0.2);
  transition-duration: 0.5s;
  --radius: 20px;
}

.u-section-4 .u-container-layout-5 {
  padding: 20px;
}

.u-section-4 .u-text-9 {
  text-transform: none;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-10 {
  margin: 3px 0 0;
}

@media (max-width: 1199px) {
   .u-section-4 {
    min-height: 1128px;
  }

  .u-section-4 .u-shape-1 {
    margin-top: 30px;
    --animation-custom_in-translate_x: 300px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-list-1 {
    width: 765px;
    margin-top: -936px;
    margin-right: calc(((100% - 940px) / 2)  + 81px);
    margin-bottom: -363px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 1069px;
  }

  .u-section-4 .u-list-item-1 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-text-1 {
    width: auto;
  }

  .u-section-4 .u-list-item-2 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-text-3 {
    width: auto;
  }

  .u-section-4 .u-list-item-3 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-text-5 {
    width: auto;
  }

  .u-section-4 .u-list-item-4 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-text-7 {
    width: auto;
  }

  .u-section-4 .u-list-item-5 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 0.3;
  }

  .u-section-4 .u-text-9 {
    width: auto;
  }
}

@media (max-width: 991px) {
   .u-section-4 {
    min-height: 1013px;
  }

  .u-section-4 .u-shape-1 {
    height: 491px;
    margin-top: 31px;
  }

  .u-section-4 .u-list-1 {
    width: 720px;
    margin-top: -952px;
    margin-right: auto;
    margin-bottom: -410px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 932px;
    grid-auto-columns: calc(100% + 0px);
  }
}

@media (max-width: 767px) {
   .u-section-4 {
    min-height: 993px;
  }

  .u-section-4 .u-shape-1 {
    margin-top: 490px;
  }

  .u-section-4 .u-list-1 {
    width: 540px;
    margin-bottom: 29px;
    margin-left: calc(((100% - 540px) / 2));
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-4 .u-container-layout-3 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-4 .u-container-layout-5 {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-list-1 {
    width: 340px;
    margin-left: calc(((100% - 340px) / 2));
  }

  .u-section-4 .u-repeater-1 {
    min-height: 439px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-4 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-4 .u-container-layout-5 {
    padding-left: 20px;
    padding-right: 20px;
  }
}