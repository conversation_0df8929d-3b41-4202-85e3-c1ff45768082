<?php
/**
 * Test skripta za proveru email funkcionalnosti
 * Pokrenite ovaj fajl u browseru da testirate email sistem
 */

require_once 'email_config.php';

// Test podaci
$test_data = array(
    'Poručuje' => '<PERSON><PERSON>',
    'Adresa' => 'K<PERSON>z <PERSON>lov<PERSON> 15',
    'Grad' => 'Beograd',
    'PoštanskiBroj' => '11000',
    'Telefon' => '+381601234567',
    'Email' => '<EMAIL>', // Promenio na pravi email za testiranje
    'textarea' => 'Ovo je test porudžbina za testiranje potvrde emaila',
    '<PERSON><PERSON><PERSON><PERSON>' => '2',
    'PotvrdaPodataka' => 'on'
);

?>
<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Sistema</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Email Sistema - Collagen Honey</h1>
    
    <div class="test-section info">
        <h3>📋 Informacije o sistemu</h3>
        <p><strong>PHP verzija:</strong> <?php echo phpversion(); ?></p>
        <p><strong>Mail funkcija:</strong> <?php echo function_exists('mail') ? '✅ Dostupna' : '❌ Nije dostupna'; ?></p>
        <p><strong>TO_EMAIL (admin):</strong> <?php echo TO_EMAIL; ?></p>
        <p><strong>FROM_EMAIL:</strong> <?php echo FROM_EMAIL; ?></p>
        <p><strong>Test email (korisnik):</strong> <?php echo $test_data['Email']; ?></p>
        <p><strong>USE_SMTP:</strong> <?php echo USE_SMTP ? 'Da' : 'Ne'; ?></p>
        <p><strong>Nova funkcionalnost:</strong> ✅ Slanje potvrde korisniku implementirano</p>
    </div>

    <?php if (isset($_POST['test_email'])): ?>
        <div class="test-section">
            <h3>🧪 Rezultat testa</h3>
            <?php
            // Simuliraj POST podatke
            $_POST = array_merge($_POST, $test_data);
            
            // Uključi glavnu skriptu
            ob_start();
            include 'process_order.php';
            $output = ob_get_clean();
            
            if (empty($output)) {
                echo '<div class="success">✅ Test je prošao! Emailovi su verovatno poslati.</div>';
                echo '<p><strong>Poslati emailovi:</strong></p>';
                echo '<ul>';
                echo '<li>📧 <strong>Admin email</strong> na: ' . TO_EMAIL . '</li>';
                echo '<li>📧 <strong>Potvrda korisniku</strong> na: ' . $test_data['Email'] . '</li>';
                echo '</ul>';
                echo '<p>Proverite oba email inbox-a (i spam folder).</p>';
            } else {
                echo '<div class="error">❌ Greška tokom testa:</div>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
            }
            ?>
        </div>
    <?php endif; ?>

    <div class="test-section">
        <h3>🎯 Test podaci</h3>
        <p>Sledeći test podaci će biti korišćeni:</p>
        <pre><?php echo htmlspecialchars(json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        
        <form method="POST">
            <button type="submit" name="test_email" value="1">🚀 Pokreni test</button>
        </form>
    </div>

    <div class="test-section">
        <h3>📝 Validacija konfiguracije</h3>
        <?php
        $config_errors = array();
        
        if (!defined('TO_EMAIL') || empty(TO_EMAIL)) {
            $config_errors[] = 'TO_EMAIL nije definisan';
        }
        
        if (!defined('FROM_EMAIL') || empty(FROM_EMAIL)) {
            $config_errors[] = 'FROM_EMAIL nije definisan';
        }
        
        if (!filter_var(TO_EMAIL, FILTER_VALIDATE_EMAIL)) {
            $config_errors[] = 'TO_EMAIL nije validna email adresa';
        }
        
        if (!filter_var(FROM_EMAIL, FILTER_VALIDATE_EMAIL)) {
            $config_errors[] = 'FROM_EMAIL nije validna email adresa';
        }
        
        if (!function_exists('mail') && !USE_SMTP) {
            $config_errors[] = 'Ni mail() funkcija ni SMTP nisu dostupni';
        }
        
        if (empty($config_errors)) {
            echo '<div class="success">✅ Konfiguracija je validna!</div>';
        } else {
            echo '<div class="error">❌ Problemi sa konfigurацијом:</div>';
            echo '<ul>';
            foreach ($config_errors as $error) {
                echo '<li>' . htmlspecialchars($error) . '</li>';
            }
            echo '</ul>';
        }
        ?>
    </div>

    <div class="test-section">
        <h3>🔧 Troubleshooting</h3>
        <ul>
            <li><strong>Email se ne šalje:</strong> Proverite spam folder, server log fajlove</li>
            <li><strong>PHP greške:</strong> Proverite error log ili uključite error reporting</li>
            <li><strong>Permission greške:</strong> Proverite da li PHP može da piše u folder</li>
            <li><strong>SMTP problemi:</strong> Proverite SMTP kredencijale i port</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>📊 Log fajl</h3>
        <?php if (file_exists(LOG_FILE)): ?>
            <?php
            $log_content = file_get_contents(LOG_FILE);
            $orders = explode('=== NOVA PORUDŽBINA ===', $log_content);
            $order_count = count($orders) - 1; // -1 jer prvi element je prazan
            ?>
            <p><strong>Log fajl postoji!</strong></p>
            <p>Ukupno porudžbina u logu: <strong><?php echo $order_count; ?></strong></p>

            <?php if ($order_count > 0): ?>
                <p><a href="view_orders.php" target="_blank" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">📋 Pogledaj sve porudžbine</a></p>

                <p>Poslednja porudžbina:</p>
                <pre style="max-height: 200px; overflow-y: auto;"><?php
                    $last_order = end($orders);
                    if (!empty(trim($last_order))) {
                        echo htmlspecialchars('=== NOVA PORUDŽBINA ===' . $last_order);
                    } else {
                        echo "Nema podataka o poslednjoj porudžbini.";
                    }
                ?></pre>
            <?php endif; ?>
        <?php else: ?>
            <p>Log fajl još ne postoji. Biće kreiran nakon prve porudžbine.</p>
            <p><strong>Format novog loga će biti:</strong></p>
            <pre>=== NOVA PORUDŽBINA ===
Datum i vreme: 01.07.2025 15:30:45
Ime i prezime: Marko Petrović
Adresa: Knez Mihailova 15
Grad: Beograd
Poštanski broj: 11000
Telefon: +381601234567
Email: <EMAIL>
Napomena: Test porudžbina
Broj kutija: 2
Cena po kutiji: 2990 RSD
UKUPNA CENA: 5980 RSD
Potvrda podataka: Da
IP adresa: ***********
User Agent: Mozilla/5.0...
========================</pre>
        <?php endif; ?>
    </div>

    <div class="test-section info">
        <h3>ℹ️ Napomene</h3>
        <ul>
            <li>Ovaj test šalje <strong>dva prava emaila</strong>: jedan administratoru i jedan korisniku kao potvrdu</li>
            <li>Email polje je sada <strong>obavezno</strong> u formi</li>
            <li>Potvrda korisniku koristi lepo formatiran template sa brend bojama (#4e3629)</li>
            <li>Uklonite ovaj fajl sa produkcijskog servera</li>
            <li>Za detaljnije testiranje, proverite server log fajlove</li>
        </ul>
    </div>
</body>
</html>
