.u-section-1 .u-sheet-1 {
  min-height: 72px;
}

.u-section-1 .u-text-1 {
  font-size: 2.25rem;
  font-weight: 700;
  width: 415px;
  margin: 16px auto -7px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-text-1 {
    --animation-custom_in-translate_x: -300px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-text-1 {
    font-size: 1.5rem;
    width: auto;
    margin: 23px 0;
  }
}.u-section-2 .u-sheet-1 {
  min-height: 512px;
}

.u-section-2 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 11px;
}

.u-section-2 .u-list-1 {
  margin-bottom: 11px;
  margin-top: 20px;
}

.u-section-2 .u-repeater-1 {
  grid-gap: 10px 10px;
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 437px;
}

.u-section-2 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-3 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-5 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-7 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-9 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-5 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-11 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-6 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-12 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-13 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-7 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-14 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-15 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-8 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-16 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-17 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-9 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-18 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-19 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-10 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-20 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-21 {
  background-image: none;
  margin: 13px 0 0 3px;
}

.u-section-2 .u-container-layout-11 {
  padding: 0 0 0 10px;
}

.u-section-2 .u-text-22 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-2 .u-text-23 {
  background-image: none;
  margin: 13px 0 0 3px;
}

@media (max-width: 1199px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 364px;
  }

  .u-section-2 .u-text-3 {
    margin-left: 0;
  }

  .u-section-2 .u-text-5 {
    margin-left: 0;
  }

  .u-section-2 .u-text-7 {
    margin-left: 0;
  }

  .u-section-2 .u-text-9 {
    margin-left: 0;
  }

  .u-section-2 .u-text-11 {
    margin-left: 0;
  }

  .u-section-2 .u-text-13 {
    margin-left: 0;
  }

  .u-section-2 .u-text-15 {
    margin-left: 0;
  }

  .u-section-2 .u-text-17 {
    margin-left: 0;
  }

  .u-section-2 .u-text-19 {
    margin-left: 0;
  }

  .u-section-2 .u-text-21 {
    margin-left: 0;
  }

  .u-section-2 .u-text-23 {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 630px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-3 .u-sheet-1 {
  min-height: 300px;
}

.u-section-3 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 20px;
}

.u-section-3 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-3 .u-repeater-1 {
  grid-gap: 10px 10px;
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 216px;
}

.u-section-3 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-3 .u-text-2 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-3 .u-text-3 {
  background-image: none;
  margin: 10px 0 0;
}

.u-section-3 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-3 .u-text-4 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-3 .u-text-5 {
  background-image: none;
  margin: 10px 0 0;
}

.u-section-3 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-3 .u-text-6 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-3 .u-text-7 {
  background-image: none;
  margin: 10px 0 0;
}

.u-section-3 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-3 .u-text-8 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-3 .u-text-9 {
  background-image: none;
  margin: 10px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 179px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 206px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-4 .u-sheet-1 {
  min-height: 512px;
}

.u-section-4 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 11px;
}

.u-section-4 .u-list-1 {
  margin-bottom: 11px;
  margin-top: 20px;
}

.u-section-4 .u-repeater-1 {
  grid-gap: 10px 10px;
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 437px;
}

.u-section-4 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-7 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-9 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-5 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-11 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-6 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-12 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-13 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-7 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-14 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-15 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-8 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-16 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-17 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-9 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-18 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-19 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-4 .u-container-layout-10 {
  padding: 0 0 0 10px;
}

.u-section-4 .u-text-20 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-4 .u-text-21 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 364px;
  }

  .u-section-4 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-9 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-11 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-13 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-15 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-17 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-19 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-text-21 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 523px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-5 .u-sheet-1 {
  min-height: 298px;
}

.u-section-5 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-5 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 20px;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 213px;
  grid-gap: 10px;
}

.u-section-5 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-5 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-5 .u-text-3 {
  background-image: none;
  margin: 12px 13px 0 3px;
}

.u-section-5 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-5 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-5 .u-text-5 {
  background-image: none;
  margin: 12px 13px 0 3px;
}

.u-section-5 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-5 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-5 .u-text-7 {
  background-image: none;
  margin: 12px 13px 0 3px;
}

.u-section-5 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-5 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-5 .u-text-9 {
  background-image: none;
  margin: 12px 13px 0 3px;
}

.u-section-5 .u-container-layout-5 {
  padding: 0 0 0 10px;
}

.u-section-5 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-5 .u-text-11 {
  background-image: none;
  margin: 12px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 177px;
  }

  .u-section-5 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-text-9 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-text-11 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 310px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-6 .u-sheet-1 {
  min-height: 185px;
}

.u-section-6 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 11px;
}

.u-section-6 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-6 .u-repeater-1 {
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-auto-columns: 100%;
  grid-gap: 10px;
}

.u-section-6 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-6 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-6 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-6 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-6 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-7 .u-sheet-1 {
  min-height: 311px;
}

.u-section-7 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 11px;
}

.u-section-7 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-7 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 227px;
  grid-gap: 10px;
}

.u-section-7 .u-container-layout-1 {
  padding: 0 10px;
}

.u-section-7 .u-text-2 {
  font-weight: 700;
  margin: 14px -10px 0 0;
}

.u-section-7 .u-text-3 {
  background-image: none;
  margin: 14px 108px 0 0;
}

.u-section-7 .u-container-layout-2 {
  padding: 0 10px;
}

.u-section-7 .u-text-4 {
  font-weight: 700;
  margin: 14px -10px 0 0;
}

.u-section-7 .u-text-5 {
  background-image: none;
  margin: 14px 108px 0 0;
}

.u-section-7 .u-container-layout-3 {
  padding: 0 10px;
}

.u-section-7 .u-text-6 {
  font-weight: 700;
  margin: 14px -10px 0 0;
}

.u-section-7 .u-text-7 {
  background-image: none;
  margin: 14px 108px 0 0;
}

.u-section-7 .u-container-layout-4 {
  padding: 0 10px;
}

.u-section-7 .u-text-8 {
  font-weight: 700;
  margin: 14px -10px 0 0;
}

.u-section-7 .u-text-9 {
  background-image: none;
  margin: 14px 108px 0 0;
}

@media (max-width: 1199px) {
  .u-section-7 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 188px;
  }

  .u-section-7 .u-text-3 {
    margin-right: 0;
  }

  .u-section-7 .u-text-5 {
    margin-right: 0;
  }

  .u-section-7 .u-text-7 {
    margin-right: 0;
  }

  .u-section-7 .u-text-9 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-7 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 216px;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-8 .u-sheet-1 {
  min-height: 185px;
}

.u-section-8 .u-text-1 {
  font-weight: 700;
  margin: 20px auto 0 11px;
}

.u-section-8 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-8 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-8 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-8 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-8 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-8 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-8 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-8 .u-text-5 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-8 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-8 .u-text-3 {
    margin-right: 0;
  }

  .u-section-8 .u-text-5 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-9 .u-sheet-1 {
  min-height: 346px;
}

.u-section-9 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-9 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 20px;
}

.u-section-9 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 260px;
  grid-gap: 10px;
}

.u-section-9 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-9 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-3 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

.u-section-9 .u-container-layout-2 {
  padding: 0 0 12px 10px;
}

.u-section-9 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-5 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

.u-section-9 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-9 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-7 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

.u-section-9 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-9 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-9 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

.u-section-9 .u-container-layout-5 {
  padding: 0 0 12px 10px;
}

.u-section-9 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-11 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

.u-section-9 .u-container-layout-6 {
  padding: 0 0 12px 10px;
}

.u-section-9 .u-text-12 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-9 .u-text-13 {
  background-image: none;
  margin: 5px 115px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-9 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 215px;
  }

  .u-section-9 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-9 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-9 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-9 .u-text-9 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-9 .u-text-11 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-9 .u-text-13 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-9 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 376px;
  }
}

@media (max-width: 767px) {
  .u-section-9 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-9 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-10 .u-sheet-1 {
  min-height: 410px;
}

.u-section-10 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-10 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 20px;
}

.u-section-10 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 325px;
  grid-gap: 10px;
}

.u-section-10 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-7 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-9 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-5 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-11 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-6 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-12 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-13 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-7 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-14 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-15 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-10 .u-container-layout-8 {
  padding: 0 0 0 10px;
}

.u-section-10 .u-text-16 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-10 .u-text-17 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-10 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 271px;
  }

  .u-section-10 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-9 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-11 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-13 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-15 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-10 .u-text-17 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-10 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 417px;
  }
}

@media (max-width: 767px) {
  .u-section-10 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-10 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-11 .u-sheet-1 {
  min-height: 298px;
}

.u-section-11 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-11 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 20px;
}

.u-section-11 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 213px;
  grid-gap: 10px;
}

.u-section-11 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-11 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-11 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-11 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-11 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-11 .u-text-5 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-11 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-11 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-11 .u-text-7 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-11 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-11 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-11 .u-text-9 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-11 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 177px;
  }

  .u-section-11 .u-text-3 {
    margin-right: 0;
  }

  .u-section-11 .u-text-5 {
    margin-right: 0;
  }

  .u-section-11 .u-text-7 {
    margin-right: 0;
  }

  .u-section-11 .u-text-9 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-11 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 203px;
  }
}

@media (max-width: 767px) {
  .u-section-11 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-11 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-12 .u-sheet-1 {
  min-height: 187px;
}

.u-section-12 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-12 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-12 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-12 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-12 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-12 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-12 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-12 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-12 .u-text-5 {
  background-image: none;
  margin: 14px 16px 0 0;
}

.u-section-12 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-12 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-12 .u-text-7 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-12 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 177px;
  }

  .u-section-12 .u-text-3 {
    margin-right: 0;
  }

  .u-section-12 .u-text-5 {
    margin-right: 0;
  }

  .u-section-12 .u-text-7 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-12 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 203px;
  }
}

@media (max-width: 767px) {
  .u-section-12 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-12 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-13 .u-sheet-1 {
  min-height: 187px;
}

.u-section-13 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-13 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-13 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-13 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-13 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-13 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-13 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-13 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-14 .u-sheet-1 {
  min-height: 187px;
}

.u-section-14 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-14 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-14 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-14 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-14 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-14 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-14 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-14 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-15 .u-sheet-1 {
  min-height: 187px;
}

.u-section-15 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-15 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-15 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-15 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-15 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-15 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-15 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-15 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-16 .u-sheet-1 {
  min-height: 187px;
}

.u-section-16 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-16 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-16 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-16 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-16 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-16 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-16 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-16 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-16 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-16 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-16 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-16 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-16 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-16 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-17 .u-sheet-1 {
  min-height: 187px;
}

.u-section-17 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-17 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-17 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-17 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-17 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-17 .u-text-3 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

@media (max-width: 1199px) {
  .u-section-17 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-17 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-18 .u-sheet-1 {
  min-height: 190px;
}

.u-section-18 .u-text-1 {
  font-weight: 700;
  margin: 22px auto 0 14px;
}

.u-section-18 .u-list-1 {
  margin-bottom: 22px;
  margin-top: 22px;
}

.u-section-18 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-18 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-18 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-18 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-18 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-18 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-18 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-18 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-18 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-18 .u-text-7 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-18 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 177px;
  }

  .u-section-18 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-18 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-18 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-18 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 203px;
  }
}

@media (max-width: 767px) {
  .u-section-18 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-18 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-19 .u-sheet-1 {
  min-height: 187px;
}

.u-section-19 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-19 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-19 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-19 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-19 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-19 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-19 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-19 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-20 .u-sheet-1 {
  min-height: 187px;
}

.u-section-20 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-20 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-20 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-20 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-20 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-20 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-20 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-20 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-21 .u-sheet-1 {
  min-height: 187px;
}

.u-section-21 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-21 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-21 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-21 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-21 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-21 .u-text-3 {
  background-image: none;
  margin: 11px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-21 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-21 .u-text-3 {
    margin-right: 0;
  }
}.u-section-22 .u-sheet-1 {
  min-height: 187px;
}

.u-section-22 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-22 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-22 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-22 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-22 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-22 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-22 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-22 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-23 .u-sheet-1 {
  min-height: 298px;
}

.u-section-23 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-23 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 20px;
}

.u-section-23 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px) calc(33.3333% - 6.66667px);
  min-height: 213px;
  grid-gap: 10px;
}

.u-section-23 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-3 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

.u-section-23 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-5 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

.u-section-23 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-7 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

.u-section-23 .u-container-layout-4 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-8 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-9 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

.u-section-23 .u-container-layout-5 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-10 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-11 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

.u-section-23 .u-container-layout-6 {
  padding: 0 0 0 10px;
}

.u-section-23 .u-text-12 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-23 .u-text-13 {
  background-image: none;
  margin: 14px 11px 0 5px;
}

@media (max-width: 1199px) {
  .u-section-23 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 177px;
  }

  .u-section-23 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-23 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-23 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-23 .u-text-9 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-23 .u-text-11 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-23 .u-text-13 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-23 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 310px;
  }
}

@media (max-width: 767px) {
  .u-section-23 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-23 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-24 .u-sheet-1 {
  min-height: 187px;
}

.u-section-24 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-24 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-24 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-24 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-24 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-24 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-24 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-24 .u-text-3 {
    margin-right: 0;
  }
}.u-section-25 .u-sheet-1 {
  min-height: 188px;
}

.u-section-25 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 8px;
}

.u-section-25 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-25 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-25 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-25 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-25 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-25 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-25 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-25 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-25 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-25 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-25 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-25 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-25 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-26 .u-sheet-1 {
  min-height: 187px;
}

.u-section-26 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-26 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-26 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-26 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-26 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-26 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-26 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-26 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-27 .u-sheet-1 {
  min-height: 187px;
}

.u-section-27 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-27 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-27 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-27 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-27 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-27 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-27 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-27 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-27 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-27 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-27 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-27 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-27 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-27 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-28 .u-sheet-1 {
  min-height: 187px;
}

.u-section-28 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-28 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-28 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-28 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-28 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-28 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-28 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-28 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-29 .u-sheet-1 {
  min-height: 187px;
}

.u-section-29 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-29 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-29 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-29 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-29 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-29 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-29 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-29 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-30 .u-sheet-1 {
  min-height: 206px;
}

.u-section-30 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-30 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-30 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 119px;
  grid-gap: 10px;
}

.u-section-30 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-30 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-30 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-30 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-30 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-30 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-30 .u-container-layout-3 {
  padding: 0 0 0 10px;
}

.u-section-30 .u-text-6 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-30 .u-text-7 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-30 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 98px;
  }

  .u-section-30 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-30 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-30 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-30 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 237px;
  }
}

@media (max-width: 767px) {
  .u-section-30 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-30 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-31 .u-sheet-1 {
  min-height: 187px;
}

.u-section-31 .u-text-1 {
  font-weight: 700;
  margin: 21px auto 0 11px;
}

.u-section-31 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 21px;
}

.u-section-31 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-31 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-31 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-31 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-31 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-31 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-32 .u-sheet-1 {
  min-height: 188px;
}

.u-section-32 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-32 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-32 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-32 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-32 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-32 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-32 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-32 .u-text-3 {
    margin-right: 0;
  }
}.u-section-33 .u-sheet-1 {
  min-height: 188px;
}

.u-section-33 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-33 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-33 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-33 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-33 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-33 .u-text-3 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

.u-section-33 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-33 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-33 .u-text-5 {
  background-image: none;
  margin: 14px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-33 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-33 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-33 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-33 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-33 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-34 .u-sheet-1 {
  min-height: 188px;
}

.u-section-34 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-34 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-34 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-34 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-34 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-34 .u-text-3 {
  background-image: none;
  margin: 16px 13px 0 3px;
}

.u-section-34 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-34 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-34 .u-text-5 {
  background-image: none;
  margin: 16px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-34 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-34 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-34 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-34 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-34 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-35 .u-sheet-1 {
  min-height: 188px;
}

.u-section-35 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-35 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-35 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-35 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-35 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-35 .u-text-3 {
  background-image: none;
  margin: 16px 13px 0 3px;
}

.u-section-35 .u-container-layout-2 {
  padding: 0 0 0 10px;
}

.u-section-35 .u-text-4 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-35 .u-text-5 {
  background-image: none;
  margin: 16px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-35 .u-repeater-1 {
    min-height: 83px;
  }

  .u-section-35 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-35 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-35 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-35 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}.u-section-36 .u-sheet-1 {
  min-height: 188px;
}

.u-section-36 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-36 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-36 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-36 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-36 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-36 .u-text-3 {
  background-image: none;
  margin: 16px 13px 0 3px;
}

@media (max-width: 1199px) {
  .u-section-36 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-36 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-37 .u-sheet-1 {
  min-height: 188px;
}

.u-section-37 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-37 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-37 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-37 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-37 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-37 .u-text-3 {
  background-image: none;
  margin: 19px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-37 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-37 .u-text-3 {
    margin-right: 0;
  }
}.u-section-38 .u-sheet-1 {
  min-height: 188px;
}

.u-section-38 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-38 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-38 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-38 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-38 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-38 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-38 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-38 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-39 .u-sheet-1 {
  min-height: 188px;
}

.u-section-39 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-39 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-39 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-39 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-39 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-39 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-39 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-39 .u-text-3 {
    margin-right: 0;
  }
}.u-section-40 .u-sheet-1 {
  min-height: 188px;
}

.u-section-40 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-40 .u-list-1 {
  margin-bottom: 21px;
  margin-top: 23px;
}

.u-section-40 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-40 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-40 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-40 .u-text-3 {
  background-image: none;
  margin: 14px 14px 0 2px;
}

@media (max-width: 1199px) {
  .u-section-40 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-40 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }
}.u-section-41 .u-sheet-1 {
  min-height: 188px;
}

.u-section-41 .u-text-1 {
  font-weight: 700;
  margin: 19px auto 0 14px;
}

.u-section-41 .u-list-1 {
  margin-bottom: 19px;
  margin-top: 25px;
}

.u-section-41 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 101px;
  grid-gap: 10px;
}

.u-section-41 .u-container-layout-1 {
  padding: 0 0 0 10px;
}

.u-section-41 .u-text-2 {
  font-weight: 700;
  margin: 14px 0 0;
}

.u-section-41 .u-text-3 {
  background-image: none;
  margin: 14px 16px 0 0;
}

@media (max-width: 1199px) {
  .u-section-41 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-41 .u-text-3 {
    margin-right: 0;
  }
}