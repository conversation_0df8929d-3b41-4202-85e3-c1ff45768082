<?php
/**
 * Test skripta za testiranje nove cenovne logike
 */

require_once 'email_config.php';

// Uključi funkcije iz process_order.php
function calculateTotalPrice($kolicina) {
    $base_price = PRICE_PER_BOX;

    switch ($kolicina) {
        case 1:
            return $base_price; // 2990 (PTT se ne uračunava u prikazanu cenu)
        case 2:
            return $base_price * 2; // 5980
        case 3:
            return DISCOUNT_3_BOXES; // 8073 (popust 10%)
        case 4:
            return $base_price * 4; // 11960
        case 5:
            return DISCOUNT_5_BOXES; // 14950 (5+1 gratis)
        case 6:
            return DISCOUNT_5_BOXES; // 14950 (ista cena kao za 5 kutija)
        default:
            // Za 7+ kutija - standardna cena bez popusta
            return $base_price * $kolicina;
    }
}

function formatPriceDisplay($kolicina, $ukupna_cena) {
    if ($kolicina == 1) {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD + PTT";
    } else {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Cenovne Logike - Collagen Honey</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-case { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #4e3629; }
        .price { font-weight: bold; color: #4e3629; font-size: 18px; }
        .description { color: #666; margin-top: 5px; }
        h1 { color: #4e3629; text-align: center; }
        h2 { color: #4e3629; border-bottom: 2px solid #4e3629; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Cenovne Logike - Collagen Honey</h1>
        
        <h2>📊 Testiranje različitih količina:</h2>
        
        <?php
        $test_cases = [
            1 => "1 kutija - + PTT (PTT se ne prikazuje u ceni)",
            2 => "2 kutije - bez PTT",
            3 => "3 kutije - popust 10%",
            4 => "4 kutije - bez PTT",
            5 => "5 kutija - 5+1 gratis",
            6 => "6 kutija - ista cena kao 5 kutija",
            7 => "7 kutija - standardna cena",
            10 => "10 kutija - standardna cena"
        ];
        
        foreach ($test_cases as $kolicina => $opis) {
            $ukupna_cena = calculateTotalPrice($kolicina);
            $display = formatPriceDisplay($kolicina, $ukupna_cena);
            
            echo "<div class='test-case'>";
            echo "<strong>$opis</strong><br>";
            echo "<div class='price'>$display</div>";
            
            // Dodaj objašnjenje
            if ($kolicina == 1) {
                echo "<div class='description'>Osnovna cena: " . PRICE_PER_BOX . " RSD + PTT (PTT zavisi od lokacije i kurirske službe)</div>";
            } elseif ($kolicina == 3) {
                $regular_price = PRICE_PER_BOX * 3;
                $discount = $regular_price - DISCOUNT_3_BOXES;
                echo "<div class='description'>Regularna cena: $regular_price RSD, Ušteda: $discount RSD (10% popust)</div>";
            } elseif ($kolicina == 5) {
                echo "<div class='description'>Specijalna ponuda: 5+1 kutija gratis</div>";
            } elseif ($kolicina == 6) {
                echo "<div class='description'>6 kutija po ceni 5+1 gratis (ista cena kao za 5 kutija)</div>";
            } else {
                echo "<div class='description'>Cena po kutiji: " . PRICE_PER_BOX . " RSD (bez PTT)</div>";
            }
            echo "</div>";
        }
        ?>
        
        <h2>⚙️ Konstante iz konfiguracije:</h2>
        <div class="test-case">
            <strong>PRICE_PER_BOX:</strong> <?php echo PRICE_PER_BOX; ?> RSD<br>
            <strong>DISCOUNT_3_BOXES:</strong> <?php echo DISCOUNT_3_BOXES; ?> RSD<br>
            <strong>DISCOUNT_5_BOXES:</strong> <?php echo DISCOUNT_5_BOXES; ?> RSD<br>
            <strong>PTT:</strong> Zavisi od lokacije i kurirske službe (ne prikazuje se u ceni)
        </div>
        
        <h2>✅ Logika je implementirana u:</h2>
        <div class="test-case">
            <ul>
                <li><strong>email_config.php</strong> - konstante za cene</li>
                <li><strong>process_order.php</strong> - funkcije calculateTotalPrice() i formatPriceDisplay()</li>
                <li><strong>Customer email</strong> - koristi formatPriceDisplay() za prikaz</li>
                <li><strong>Admin email</strong> - koristi formatPriceDisplay() za prikaz</li>
                <li><strong>Log fajl</strong> - detaljno prikazuje cenu sa objašnjenjem</li>
            </ul>
        </div>
    </div>
</body>
</html>
