<?php
/**
 * Test skripta za testiranje samo customer email funkcionalnosti
 */

require_once 'email_config.php';

// Uključi funkcije iz process_order.php
function sendEmail($to, $subject, $message, $headers) {
    if (USE_SMTP) {
        return false; // SMTP nije implementiran
    } else {
        return mail($to, $subject, $message, $headers);
    }
}

// Funkcija za izračunavanje ukupne cene sa popustima
function calculateTotalPrice($kolicina) {
    $base_price = PRICE_PER_BOX;

    switch ($kolicina) {
        case 1:
            return $base_price; // 2990 (PTT se ne uračunava u prikazanu cenu)
        case 2:
            return $base_price * 2; // 5980
        case 3:
            return DISCOUNT_3_BOXES; // 8073 (popust 10%)
        case 4:
            return $base_price * 4; // 11960
        case 5:
            return DISCOUNT_5_BOXES; // 14950 (5+1 gratis)
        case 6:
            return DISCOUNT_5_BOXES; // 14950 (ista cena kao za 5 kutija)
        default:
            // Za 7+ kutija - standardna cena bez popusta
            return $base_price * $kolicina;
    }
}

// Funkcija za formatiranje prikaza cene
function formatPriceDisplay($kolicina, $ukupna_cena) {
    if ($kolicina == 1) {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD + PTT";
    } else {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD";
    }
}

// Kopiraj funkciju sendCustomerConfirmation iz process_order.php
function sendCustomerConfirmation($customer_email, $ime_prezime, $kolicina, $ukupna_cena, $adresa, $grad, $postanski_broj, $telefon, $napomena) {
    // Kreiraj sadržaj potvrde za korisnika
    $customer_message = "
    <html>
    <head>
        <title>Potvrda porudžbine - Collagen Honey</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #4e3629; background-color: #f1e3c6; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f1e3c6; }
            .header { background-color: #4e3629; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px 20px; background-color: #f1e3c6; }
            .greeting { font-size: 18px; margin-bottom: 20px; color: #4e3629; font-weight: bold; }
            .order-details { background-color: #f1e3c6; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4e3629; }
            .field { margin-bottom: 12px; display: flex; }
            .label { font-weight: bold; color: #4e3629; min-width: 140px; }
            .value { color: #4e3629; flex: 1; }
            .total { background-color: #4e3629; color: white; padding: 15px; text-align: center; font-size: 20px; font-weight: bold; border-radius: 8px; margin: 20px 0; }
            .footer { background-color: #4e3629; color: white; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
            .footer p { margin: 5px 0; }
            .note { background-color: #f1e3c6; border: 1px solid #4e3629; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .note strong { color: #4e3629; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🍯 Collagen Honey</h1>
                <p style='margin: 10px 0 0 0; font-size: 16px;'>Potvrda vaše porudžbine</p>
            </div>
            <div class='content'>
                <div class='greeting'>
                    Poštovani/a $ime_prezime,
                </div>
                <p>Hvala vam što ste izabrali Collagen Honey! Vaša porudžbina je uspešno primljena i u procesu je obrade.</p>
                
                <div class='order-details'>
                    <h3 style='color: #4e3629; margin-top: 0;'>📋 Detalji vaše porudžbine:</h3>
                    <div class='field'>
                        <span class='label'>Ime i prezime:</span> 
                        <span class='value'>$ime_prezime</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Adresa dostave:</span> 
                        <span class='value'>$adresa</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Grad:</span> 
                        <span class='value'>$grad</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Poštanski broj:</span> 
                        <span class='value'>$postanski_broj</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Kontakt telefon:</span> 
                        <span class='value'>$telefon</span>
                    </div>";
    
    if (!empty($napomena)) {
        $customer_message .= "
                    <div class='field'>
                        <span class='label'>Napomena:</span> 
                        <span class='value'>$napomena</span>
                    </div>";
    }
    
    $cena_po_kutiji = PRICE_PER_BOX;
    $customer_message .= "
                    <div class='field'>
                        <span class='label'>Broj kutija:</span> 
                        <span class='value'>$kolicina</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Cena po kutiji:</span> 
                        <span class='value'>$cena_po_kutiji RSD</span>
                    </div>
                </div>
                
                <div class='total'>
                    " . formatPriceDisplay($kolicina, $ukupna_cena) . "
                </div>
                
                <p>Hvala vam na poverenju! 🙏</p>
                <p><strong>Tim Collagen Honey</strong></p>
            </div>
            <div class='footer'>
                <p><strong>Collagen Honey</strong></p>
                <p>📍 Obrenovac, Banjanski put 26v</p>
                <p>📧 <EMAIL> | 📱 +381 60 370 00 37</p>
                <p style='font-size: 12px; margin-top: 15px;'>
                    Porudžbina poslata: " . date('d.m.Y u H:i') . "
                </p>
            </div>
        </div>
    </body>
    </html>";
    
    // Email headers za potvrdu korisniku
    $customer_headers = "MIME-Version: 1.0" . "\r\n";
    $customer_headers .= "Content-Type: text/html; charset=UTF-8" . "\r\n";
    $customer_headers .= "Content-Transfer-Encoding: 8bit" . "\r\n";
    $customer_headers .= "From: Collagen Honey <" . FROM_EMAIL . ">" . "\r\n";
    $customer_headers .= "Reply-To: Collagen Honey <" . REPLY_TO_EMAIL . ">" . "\r\n";
    $customer_headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $customer_headers .= "X-Priority: 3" . "\r\n";
    $customer_headers .= "X-MSMail-Priority: Normal" . "\r\n";
    $customer_headers .= "Importance: Normal" . "\r\n";
    
    $customer_subject = "Potvrda porudžbine - Collagen Honey";
    
    return sendEmail($customer_email, $customer_subject, $customer_message, $customer_headers);
}

// Test podaci
$test_email = '<EMAIL>';
$test_ime = 'Nenad Stojković';
$test_kolicina = 1; // Testiraj sa 1 kutijom da vidiš PTT
$test_cena = calculateTotalPrice($test_kolicina);
$test_adresa = 'Knez Mihailova 15';
$test_grad = 'Beograd';
$test_postanski = '11000';
$test_telefon = '+381 60 123 4567';
$test_napomena = 'Test porudžbina za proveru email template-a';

?>
<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Customer Email</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { background: #4e3629; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #3a2a1f; }
    </style>
</head>
<body>
    <h1>🧪 Test Customer Email Template</h1>
    
    <div class="test-section">
        <h3>📧 Test podaci:</h3>
        <p><strong>Email:</strong> <?php echo $test_email; ?></p>
        <p><strong>Ime:</strong> <?php echo $test_ime; ?></p>
        <p><strong>Količina:</strong> <?php echo $test_kolicina; ?> kutija</p>
        <p><strong>Ukupno:</strong> <?php echo $test_cena; ?> RSD</p>
    </div>

    <?php if (isset($_POST['send_test'])): ?>
        <div class="test-section">
            <h3>📤 Rezultat slanja:</h3>
            <?php
            $result = sendCustomerConfirmation(
                $test_email, 
                $test_ime, 
                $test_kolicina, 
                $test_cena, 
                $test_adresa, 
                $test_grad, 
                $test_postanski, 
                $test_telefon, 
                $test_napomena
            );
            
            if ($result) {
                echo '<div class="success">✅ Email je uspešno poslat na ' . $test_email . '</div>';
                echo '<p>Proverite inbox (i spam folder).</p>';
            } else {
                echo '<div class="error">❌ Greška pri slanju emaila.</div>';
                echo '<p>Proverite email konfiguraciju i server log.</p>';
            }
            ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <button type="submit" name="send_test" value="1">📧 Pošalji test email</button>
    </form>
</body>
</html>
