<?php
// Učitaj konfiguraciju
require_once 'email_config.php';

// Funkcija za slanje emaila
function sendEmail($to, $subject, $message, $headers) {
    if (USE_SMTP) {
        // SMTP slanje (potrebna je PHPMailer biblioteka)
        // Ova funkcionalnost zahteva instalaciju PHPMailer-a
        // composer require phpmailer/phpmailer

        /*
        use PHPMailer\PHPMailer\PHPMailer;
        use PHPMailer\PHPMailer\SMTP;
        use PHPMailer\PHPMailer\Exception;

        require 'vendor/autoload.php';

        $mail = new PHPMailer(true);

        try {
            $mail->isSMTP();
            $mail->Host       = SMTP_HOST;
            $mail->SMTPAuth   = true;
            $mail->Username   = SMTP_USERNAME;
            $mail->Password   = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_SECURE;
            $mail->Port       = SMTP_PORT;
            $mail->CharSet    = 'UTF-8';

            $mail->setFrom(FROM_EMAIL, 'Collagen Honey');
            $mail->addAddress($to);

            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body    = $message;

            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("SMTP Error: {$mail->ErrorInfo}");
            return false;
        }
        */

        // Za sada vraćamo false jer SMTP nije implementiran
        return false;
    } else {
        // Koristi PHP mail() funkciju
        return mail($to, $subject, $message, $headers);
    }
}

// Funkcija za izračunavanje ukupne cene sa popustima
function calculateTotalPrice($kolicina) {
    $base_price = PRICE_PER_BOX;

    switch ($kolicina) {
        case 1:
            return $base_price; // 2990 (PTT se ne uračunava u prikazanu cenu)
        case 2:
            return $base_price * 2; // 5980
        case 3:
            return DISCOUNT_3_BOXES; // 8073 (popust 10%)
        case 4:
            return $base_price * 4; // 11960
        case 5:
            return DISCOUNT_5_BOXES; // 14950 (5+1 gratis)
        case 6:
            return DISCOUNT_5_BOXES; // 14950 (ista cena kao za 5 kutija)
        default:
            // Za 7+ kutija - standardna cena bez popusta
            return $base_price * $kolicina;
    }
}

// Funkcija za formatiranje prikaza cene
function formatPriceDisplay($kolicina, $ukupna_cena) {
    if ($kolicina == 1) {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD + PTT";
    } else {
        return "💰 UKUPNO ZA PLAĆANJE: $ukupna_cena RSD";
    }
}

// Funkcija za slanje potvrde korisniku
function sendCustomerConfirmation($customer_email, $ime_prezime, $kolicina, $ukupna_cena, $adresa, $grad, $postanski_broj, $telefon, $napomena) {
    // Kreiraj sadržaj potvrde za korisnika
    $customer_message = "
    <html>
    <head>
        <title>Potvrda porudžbine - Collagen Honey</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #4e3629; background-color: white; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: white; }
            .header { background-color: #f1e3c6; color: #4e3629; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px 20px; background-color: white; }
            .greeting { font-size: 18px; margin-bottom: 20px; color: #4e3629; font-weight: bold; }
            .order-details { background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f1e3c6; }
            .field { margin-bottom: 12px; display: flex; }
            .label { font-weight: bold; color: #4e3629; min-width: 140px; }
            .value { color: #4e3629; flex: 1; }
            .total { background-color: #f1e3c6; color: #4e3629; padding: 15px; text-align: center; font-size: 20px; font-weight: bold; border-radius: 8px; margin: 20px 0; }
            .footer { background-color: #f1e3c6; color: #4e3629; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
            .footer p { margin: 5px 0; }
            .note { background-color: white; border: 1px solid #f1e3c6; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .note strong { color: #4e3629; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🍯 Collagen Honey</h1>
                <p style='margin: 10px 0 0 0; font-size: 16px;'>Potvrda vaše porudžbine</p>
            </div>
            <div class='content'>
                <div class='greeting'>
                    Poštovani/a $ime_prezime,
                </div>
                <p>Hvala vam što ste izabrali Collagen Honey! Vaša porudžbina je uspešno primljena i u procesu je obrade.</p>

                <div class='order-details'>
                    <h3 style='color: #4e3629; margin-top: 0;'>📋 Detalji vaše porudžbine:</h3>
                    <div class='field'>
                        <span class='label'>Ime i prezime:</span>
                        <span class='value'>$ime_prezime</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Adresa dostave:</span>
                        <span class='value'>$adresa</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Grad:</span>
                        <span class='value'>$grad</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Poštanski broj:</span>
                        <span class='value'>$postanski_broj</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Kontakt telefon:</span>
                        <span class='value'>$telefon</span>
                    </div>";

    if (!empty($napomena)) {
        $customer_message .= "
                    <div class='field'>
                        <span class='label'>Napomena:</span>
                        <span class='value'>$napomena</span>
                    </div>";
    }

    $cena_po_kutiji = PRICE_PER_BOX;
    $customer_message .= "
                    <div class='field'>
                        <span class='label'>Broj kutija:</span>
                        <span class='value'>$kolicina</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Cena po kutiji:</span>
                        <span class='value'>$cena_po_kutiji RSD</span>
                    </div>
                </div>

                <div class='total'>
                    " . formatPriceDisplay($kolicina, $ukupna_cena) . "
                </div>

                <p>Hvala vam na poverenju! 🙏</p>
                <p><strong>Tim Collagen Honey</strong></p>
            </div>
            <div class='footer'>
                <p><strong>Collagen Honey</strong></p>
                <p>📍 Obrenovac, Banjanski put 26v</p>
                <p>📧 <EMAIL> | 📱 +381 60 370 00 37</p>
                <p style='font-size: 12px; margin-top: 15px;'>
                    Porudžbina poslata: " . date('d.m.Y u H:i') . "
                </p>
            </div>
        </div>
    </body>
    </html>";

    // Email headers za potvrdu korisniku
    $customer_headers = "MIME-Version: 1.0" . "\r\n";
    $customer_headers .= "Content-Type: text/html; charset=UTF-8" . "\r\n";
    $customer_headers .= "Content-Transfer-Encoding: 8bit" . "\r\n";
    $customer_headers .= "From: Collagen Honey <" . FROM_EMAIL . ">" . "\r\n";
    $customer_headers .= "Reply-To: Collagen Honey <" . REPLY_TO_EMAIL . ">" . "\r\n";
    $customer_headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $customer_headers .= "X-Priority: 3" . "\r\n";
    $customer_headers .= "X-MSMail-Priority: Normal" . "\r\n";
    $customer_headers .= "Importance: Normal" . "\r\n";

    $customer_subject = "Potvrda porudžbine - Collagen Honey";

    return sendEmail($customer_email, $customer_subject, $customer_message, $customer_headers);
}

// Proveri da li je forma poslata POST metodom
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Sanitizuj i validuj podatke
    $ime_prezime = isset($_POST['Poručuje']) ? htmlspecialchars(trim($_POST['Poručuje'])) : '';
    $adresa = isset($_POST['Adresa']) ? htmlspecialchars(trim($_POST['Adresa'])) : '';
    $grad = isset($_POST['Grad']) ? htmlspecialchars(trim($_POST['Grad'])) : '';
    $postanski_broj = isset($_POST['PoštanskiBroj']) ? htmlspecialchars(trim($_POST['PoštanskiBroj'])) : '';
    $telefon = isset($_POST['Telefon']) ? htmlspecialchars(trim($_POST['Telefon'])) : '';
    $email = isset($_POST['Email']) ? htmlspecialchars(trim($_POST['Email'])) : '';
    $napomena = isset($_POST['textarea']) ? htmlspecialchars(trim($_POST['textarea'])) : '';
    $kolicina = isset($_POST['Količina']) ? intval($_POST['Količina']) : 1;
    $potvrda_podataka = isset($_POST['PotvrdaPodataka']) ? 'Da' : 'Ne';
    
    // Validacija obaveznih polja
    $errors = array();

    if (empty($ime_prezime)) {
        $errors[] = $validation_messages['ime_prezime_required'];
    }

    if (empty($adresa)) {
        $errors[] = $validation_messages['adresa_required'];
    }

    if (empty($grad)) {
        $errors[] = $validation_messages['grad_required'];
    }

    if (empty($postanski_broj)) {
        $errors[] = $validation_messages['postanski_broj_required'];
    }

    if (empty($telefon)) {
        $errors[] = $validation_messages['telefon_required'];
    }

    if ($kolicina < 1) {
        $errors[] = $validation_messages['kolicina_invalid'];
    }

    if ($potvrda_podataka !== 'Da') {
        $errors[] = $validation_messages['potvrda_required'];
    }

    // Validacija email adrese - sada je obavezno polje
    if (empty($email)) {
        $errors[] = $validation_messages['email_required'];
    } elseif (ENABLE_EMAIL_VALIDATION && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = $validation_messages['email_invalid'];
    }
    
    // Ako nema grešaka, pošalji email
    if (empty($errors)) {
        
        // Izračunaj ukupnu cenu sa popustima i PTT
        $ukupna_cena = calculateTotalPrice($kolicina);
        
        // Kreiraj sadržaj emaila
        $message = "
        <html>
        <head>
            <title>Nova porudžbina - Collagen Honey</title>
            <style>
                body { font-family: Arial, sans-serif; color: #4e3629; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #f1e3c6; color: #4e3629; padding: 15px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .field { margin-bottom: 10px; }
                .label { font-weight: bold; color: #4e3629; }
                .value { color: #4e3629; }
                .total { background-color: #f1e3c6; color: #4e3629; padding: 10px; text-align: center; font-size: 18px; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Nova porudžbina - Collagen Honey</h2>
                </div>
                <div class='content'>
                    <div class='field'>
                        <span class='label'>Ime i prezime:</span> 
                        <span class='value'>$ime_prezime</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Adresa:</span> 
                        <span class='value'>$adresa</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Grad:</span> 
                        <span class='value'>$grad</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Poštanski broj:</span> 
                        <span class='value'>$postanski_broj</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Telefon:</span> 
                        <span class='value'>$telefon</span>
                    </div>";
        
        if (!empty($email)) {
            $message .= "
                    <div class='field'>
                        <span class='label'>Email:</span> 
                        <span class='value'>$email</span>
                    </div>";
        }
        
        if (!empty($napomena)) {
            $message .= "
                    <div class='field'>
                        <span class='label'>Napomena:</span> 
                        <span class='value'>$napomena</span>
                    </div>";
        }
        
        $message .= "
                    <div class='field'>
                        <span class='label'>Broj kutija:</span> 
                        <span class='value'>$kolicina</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Cena po kutiji:</span> 
                        <span class='value'>$cena_po_kutiji RSD</span>
                    </div>
                </div>
                <div class='total'>
                    " . str_replace('💰 ', '', formatPriceDisplay($kolicina, $ukupna_cena)) . "
                </div>
                <div style='padding: 15px; text-align: center; color: #666; font-size: 12px;'>
                    Porudžbina je stigla sa sajta collagenhoney.com<br>
                    Datum: " . date('d.m.Y H:i:s') . "
                </div>
            </div>
        </body>
        </html>";
        
        // Email headers optimizovani za Outlook
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8" . "\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit" . "\r\n";
        $headers .= "From: Collagen Honey <" . FROM_EMAIL . ">" . "\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        $headers .= "X-Priority: 3" . "\r\n";
        $headers .= "X-MSMail-Priority: Normal" . "\r\n";
        $headers .= "Importance: Normal" . "\r\n";

        if (!empty($email)) {
            $headers .= "Reply-To: $ime_prezime <$email>" . "\r\n";
        } else {
            $headers .= "Reply-To: Collagen Honey <" . REPLY_TO_EMAIL . ">" . "\r\n";
        }

        // Logiraj porudžbinu ako je omogućeno
        if (ENABLE_LOGGING) {
            $log_entry = "=== NOVA PORUDŽBINA ===\n";
            $log_entry .= "Datum i vreme: " . date('d.m.Y H:i:s') . "\n";
            $log_entry .= "Ime i prezime: $ime_prezime\n";
            $log_entry .= "Adresa: $adresa\n";
            $log_entry .= "Grad: $grad\n";
            $log_entry .= "Poštanski broj: $postanski_broj\n";
            $log_entry .= "Telefon: $telefon\n";

            if (!empty($email)) {
                $log_entry .= "Email: $email\n";
            }

            if (!empty($napomena)) {
                $log_entry .= "Napomena: $napomena\n";
            }

            $log_entry .= "Broj kutija: $kolicina\n";

            // Detaljno prikaži cenu
            if ($kolicina == 1) {
                $base_price = PRICE_PER_BOX;
                $log_entry .= "Cena po kutiji: $base_price RSD\n";
                $log_entry .= "UKUPNA CENA: $ukupna_cena RSD + PTT (PTT zavisi od lokacije)\n";
            } elseif ($kolicina == 3) {
                $log_entry .= "Cena: 3 kutije sa popustom 10%\n";
                $log_entry .= "UKUPNA CENA: $ukupna_cena RSD (bez PTT)\n";
            } elseif ($kolicina == 5) {
                $log_entry .= "Cena: 5+1 kutija gratis\n";
                $log_entry .= "UKUPNA CENA: $ukupna_cena RSD (bez PTT)\n";
            } elseif ($kolicina == 6) {
                $log_entry .= "Cena: 6 kutija po ceni 5+1 gratis\n";
                $log_entry .= "UKUPNA CENA: $ukupna_cena RSD (bez PTT)\n";
            } else {
                $base_price = PRICE_PER_BOX;
                $log_entry .= "Cena po kutiji: $base_price RSD\n";
                $log_entry .= "UKUPNA CENA: $ukupna_cena RSD (bez PTT)\n";
            }
            $log_entry .= "Potvrda podataka: $potvrda_podataka\n";

            // Dodaj dodatne informacije ako je omogućeno detaljno logovanje
            if (defined('DETAILED_LOGGING') && DETAILED_LOGGING) {
                $log_entry .= "IP adresa: " . ($_SERVER['REMOTE_ADDR'] ?? 'N/A') . "\n";
                $log_entry .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') . "\n";
                $log_entry .= "Referer: " . ($_SERVER['HTTP_REFERER'] ?? 'N/A') . "\n";

                // Dodaj informacije o browseru ako su dostupne
                if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
                    $log_entry .= "Jezik: " . $_SERVER['HTTP_ACCEPT_LANGUAGE'] . "\n";
                }
            }

            $log_entry .= "========================\n\n";

            file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
        }

        // Pošalji email administratoru
        $admin_email_sent = sendEmail(TO_EMAIL, EMAIL_SUBJECT, $message, $headers);

        // Pošalji potvrdu korisniku
        $customer_email_sent = false;
        if (!empty($email) && $admin_email_sent) {
            $customer_email_sent = sendCustomerConfirmation($email, $ime_prezime, $kolicina, $ukupna_cena, $adresa, $grad, $postanski_broj, $telefon, $napomena);
        }

        if ($admin_email_sent) {
            if ($customer_email_sent) {
                // Oba emaila uspešno poslata
                header("Location: Poručivanje.html?status=success&msg=" . urlencode("Vaša porudžbina je uspešno poslata i dobićete potvrdu na vašu email adresu."));
            } else {
                // Samo admin email poslat
                header("Location: Poručivanje.html?status=success&msg=" . urlencode("Vaša porudžbina je uspešno poslata."));
            }
            exit();
        } else {
            // Greška pri slanju admin emaila
            header("Location: Poručivanje.html?status=error&msg=" . urlencode($validation_messages['email_send_error']));
            exit();
        }
        
    } else {
        // Ima grešaka - preusmeri sa greškama
        $error_msg = implode(" ", $errors);
        header("Location: Poručivanje.html?status=error&msg=" . urlencode($error_msg));
        exit();
    }
    
} else {
    // Nije POST metoda
    header("Location: Poručivanje.html");
    exit();
}
?>
