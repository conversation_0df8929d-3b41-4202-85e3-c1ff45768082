# Collagen Honey - Email Setup za Porudžbine

## Pregled

Ovaj sistem omogućava slanje emailova sa podacima o porudžbinama direktno sa vašeg sajta. Kada korisnik popuni formu za poručivanje, podaci se šalju na vaš email.

## Fajlovi

1. **process_order.php** - Glavna skripta koja obrađuje formu i šalje email
2. **email_config.php** - Konfiguracija za email postavke
3. **view_orders.php** - Pregled svih porudžbina iz log fajla
4. **test_form.html** - Čista test forma bez JavaScript interferiranja
5. **test_email.php** - Test skripta za proveru sistema
6. **Poručivanje.html** - Ažurirana forma koja poziva PHP skriptu
7. **README_EMAIL_SETUP.md** - Ovaj fajl sa instrukcijama

## Podešavanje

### 1. Osnovne postavke

Otvorite `email_config.php` i prilagodite sledeće postavke:

```php
// Email adrese
define('TO_EMAIL', '<EMAIL>');        // Vaš email
define('FROM_EMAIL', '<EMAIL>');     // Email sa kojeg se šalje
define('REPLY_TO_EMAIL', '<EMAIL>');  // Reply-to email

// Cene
define('PRICE_PER_BOX', 2990); // Cena po kutiji u dinarima
```

### 2. Server zahtevi

- PHP 5.6 ili noviji
- Omogućena `mail()` funkcija na serveru
- Ili SMTP server za slanje emailova

### 3. Testiranje

1. Postavite fajlove na vaš web server
2. Otvorite `Poručivanje.html` u browseru
3. Popunite formu sa test podacima
4. Kliknite "PORUČI"
5. Proverite da li je email stigao

### 4. SMTP konfiguracija (opciono)

Ako vaš server ne podržava PHP `mail()` funkciju, možete koristiti SMTP:

1. Instalirajte PHPMailer:
   ```bash
   composer require phpmailer/phpmailer
   ```

2. U `email_config.php` uklonite komentare i prilagodite:
   ```php
   define('USE_SMTP', true);
   define('SMTP_HOST', 'smtp.yourdomain.com');
   define('SMTP_PORT', 587);
   define('SMTP_USERNAME', '<EMAIL>');
   define('SMTP_PASSWORD', 'your-password');
   define('SMTP_SECURE', 'tls');
   ```

3. U `process_order.php` uklonite komentare sa SMTP kodom

## Funkcionalnosti

### Validacija
- Proverava sva obavezna polja
- Validira email format
- Proverava količinu (minimum 1)
- Zahteva potvrdu podataka

### Email sadržaj
- Formatiran HTML email
- Svi podaci iz forme
- Automatski izračun ukupne cene
- Datum i vreme porudžbine

### Logovanje
- Automatski log svih porudžbina u `orders.log`
- Detaljne informacije: ime, adresa, telefon, email, napomena, količina, cena
- Dodatne informacije: IP adresa, User Agent, datum/vreme
- Može se isključiti u konfiguraciji
- Pregled svih porudžbina: otvorite `view_orders.php` u browseru

### Poruke korisniku
- Uspešna porudžbina: zelena poruka sa countdown timerom
- Greška: crvena poruka sa detaljima i countdown timerom
- Poruke se prikazuju 10 sekundi sa vizuelnim indikatorom
- Dugme za poručivanje se automatski sakrije/prikaže
- Konfigurabilno vreme prikazivanja u `email_config.php`

## Bezbednost

### Preporučene mere:
1. Koristite HTTPS za formu
2. Implementirajte rate limiting
3. Dodajte CAPTCHA zaštitu
4. Redovno proveravajte log fajlove

### Spam zaštita:
- Forma već ima reCAPTCHA integraciju
- Validacija svih polja
- Sanitizacija podataka

## Troubleshooting

### Greška 515 (nicepagesrv.com):
Ova greška se javlja kada postojeći JavaScript kod pokušava da pošalje podatke na originalni servis umesto na našu PHP skriptu.

**Rešenje:**
1. Koristite `test_form.html` za testiranje - ova forma radi direktno bez interferiranja
2. Ako i dalje imate probleme, dodajte sledeći kod na vrh `Poručivanje.html` (nakon `<body>` taga):

```html
<script>
// Potpuno onemogući postojeći form sistem
window.addEventListener('load', function() {
    // Ukloni sve postojeće event listenere
    document.querySelectorAll('script').forEach(function(script) {
        if (script.innerHTML.includes('nicepagesrv') || script.innerHTML.includes('recaptcha')) {
            script.remove();
        }
    });
});
</script>
```

### Email se ne šalje:
1. Proverite da li server podržava `mail()` funkciju
2. Proverite spam folder
3. Proverite server log fajlove
4. Pokušajte sa SMTP konfigurацијом

### Greške u formi:
1. Proverite da li su svi fajlovi na serveru
2. Proverite PHP verziju
3. Proverite file permissions (755 za foldere, 644 za fajlove)

### Test forma:
- Koristite `test_form.html` za čisto testiranje bez postojećeg JavaScript koda
- Ova forma direktno poziva `process_order.php`
- Ako test forma radi, problem je u JavaScript interferiranju

### Log fajl:
- Sve porudžbine se loguju u `orders.log`
- Detaljne informacije o svakoj porudžbini
- Pregled: otvorite `view_orders.php` u browseru
- Pretraga po imenu, telefonu, gradu, email adresi
- Statistike: ukupno porudžbina, kutija, prihod

## Prilagođavanje

### Dodavanje novih polja:
1. Dodajte polje u HTML formu
2. Dodajte validaciju u `process_order.php`
3. Dodajte polje u email template

### Menjanje dizajna emaila:
- Uredite HTML template u `process_order.php`
- Koristite inline CSS stilove

### Dodavanje dodatnih email adresa:
```php
// U process_order.php dodajte:
$additional_emails = array('<EMAIL>', '<EMAIL>');
foreach($additional_emails as $email) {
    sendEmail($email, EMAIL_SUBJECT, $message, $headers);
}
```

## Kontakt

Za dodatnu pomoć oko podešavanja, kontaktirajte vašeg web developera ili hosting provajdera.
