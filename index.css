.u-section-1 .u-sheet-1 {
  min-height: 806px;
}

.u-section-1 .u-text-1 {
  text-shadow: 0px 0px 4px rgba(78,54,41,1);
  font-size: 2.25rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 60px 0 0;
}

.u-section-1 .u-btn-1 {
  --radius: 50px;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 1px;
  background-image: none;
  margin: 526px auto 60px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 842px;
  }

  .u-section-1 .u-btn-1 {
    margin-top: 562px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-text-1 {
    font-size: 1.875rem;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-text-1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-text-1 {
    font-size: 0.875rem;
    width: auto;
  }
}.u-section-2 .u-sheet-1 {
  min-height: 100vh;
}

.u-section-2 .u-text-1 {
  text-shadow: 0px 0px 4px rgba(78,54,41,1);
  font-size: 2.25rem;
  --animation-custom_in-translate_x: -200px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 100px 0 0;
}

.u-section-2 .u-btn-1 {
  --radius: 50px;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 1px;
  background-image: none;
  margin: 553px auto 60px;
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 905px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.875rem;
  }

  .u-section-2 .u-btn-1 {
    margin-top: 641px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-text-1 {
    font-size: 1.5rem;
  }

  .u-section-2 .u-btn-1 {
    margin-top: 687px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 702px;
  }

  .u-section-2 .u-text-1 {
    font-size: 0.875rem;
    width: auto;
    margin-top: 75px;
  }

  .u-section-2 .u-btn-1 {
    --animation-custom_in-translate_x: -200px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
    margin-top: 504px;
  }
} .u-section-3 {
  background-image: none;
  min-height: 596px;
}

.u-section-3 .u-group-1 {
  min-height: 376px;
  height: auto;
  margin-top: 0;
  margin-bottom: 60px;
}

.u-section-3 .u-container-layout-1 {
  padding: 0 30px;
}

.u-section-3 .u-list-1 {
  width: 850px;
  grid-template-rows: repeat(1, auto);
  grid-auto-rows: 100%;
  margin: 32px auto -184px;
}

.u-section-3 .u-repeater-1 {
  grid-template-columns: repeat(3, calc(33.3333% - 14px));
  min-height: 528px;
  grid-auto-columns: calc(33.3333% - 14px);
  grid-gap: 21px;
}

.u-section-3 .u-list-item-1 {
  background-image: none;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-2 {
  padding: 20px;
}

.u-section-3 .u-icon-1 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-1 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

.u-section-3 .u-list-item-2 {
  background-size: auto;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-3 {
  padding: 20px;
}

.u-section-3 .u-icon-2 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-2 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

.u-section-3 .u-list-item-3 {
  background-size: auto;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-4 {
  padding: 20px;
}

.u-section-3 .u-icon-3 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-3 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

.u-section-3 .u-list-item-4 {
  background-size: auto;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-5 {
  padding: 20px;
}

.u-section-3 .u-icon-4 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-4 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

.u-section-3 .u-list-item-5 {
  background-size: auto;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-6 {
  padding: 20px;
}

.u-section-3 .u-icon-5 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-5 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

.u-section-3 .u-list-item-6 {
  background-size: auto;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 30px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-container-layout-7 {
  padding: 20px;
}

.u-section-3 .u-icon-6 {
  width: 92px;
  height: 92px;
  background-image: none;
  margin: 23px auto 0;
  padding: 9px;
}

.u-section-3 .u-text-6 {
  letter-spacing: 2px;
  font-size: 1.125rem;
  font-weight: 700;
  font-style: normal;
  text-transform: none;
  margin: 23px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-group-1 {
    height: auto;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(3, calc(33.333333333333336% - 14px));
    grid-auto-columns: calc(33.333333333333336% - 14px);
  }
}

@media (max-width: 991px) {
   .u-section-3 {
    min-height: 852px;
  }

  .u-section-3 .u-group-1 {
    min-height: 792px;
  }

  .u-section-3 .u-list-1 {
    width: 660px;
    margin-bottom: -26px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 10.5px));
    min-height: 786px;
    grid-auto-columns: calc(50% - 10.5px);
  }

  .u-section-3 .u-container-layout-2 {
    padding-bottom: 29px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-bottom: 29px;
  }

  .u-section-3 .u-container-layout-7 {
    padding-bottom: 29px;
  }
}

@media (max-width: 767px) {
   .u-section-3 {
    min-height: 825px;
  }

  .u-section-3 .u-group-1 {
    min-height: 615px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-list-1 {
    width: 520px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 767px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 23px;
  }

  .u-section-3 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 23px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 23px;
  }
}

@media (max-width: 575px) {
   .u-section-3 {
    min-height: 645px;
  }

  .u-section-3 .u-group-1 {
    margin-bottom: 30px;
  }

  .u-section-3 .u-list-1 {
    width: 320px;
    margin-bottom: -4px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 10px));
    min-height: 587px;
    grid-auto-columns: calc(50% - 10px);
    grid-gap: 20px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-1 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-1 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }

  .u-section-3 .u-container-layout-3 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-2 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-2 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-3 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-3 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-4 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-4 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-5 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-5 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }

  .u-section-3 .u-container-layout-7 {
    padding-bottom: 9px;
  }

  .u-section-3 .u-icon-6 {
    width: 74px;
    height: 74px;
    margin-top: 0;
    padding: 7px;
  }

  .u-section-3 .u-text-6 {
    font-size: 0.75rem;
    width: auto;
    margin-top: 18px;
  }
}.u-section-4 .u-sheet-1 {
  min-height: 1025px;
}

.u-section-4 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
}

.u-section-4 .u-layout-cell-1 {
  min-height: 905px;
  --top-left-radius: 20px;
}

.u-section-4 .u-container-layout-1 {
  padding: 30px 30px 30px 0;
}

.u-section-4 .u-image-1 {
  height: 695px;
  object-position: 55.03% 0.15%;
  margin-top: 75px;
  margin-bottom: 0;
}

.u-section-4 .u-layout-cell-2 {
  min-height: 906px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-4 .u-container-layout-2 {
  padding: 20px;
}

.u-section-4 .u-list-1 {
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 197px;
  grid-gap: 10px;
}

.u-section-4 .u-container-layout-3 {
  padding: 10px 10px 9px;
}

.u-section-4 .u-text-1 {
  font-weight: 700;
  font-style: normal;
  margin: 11px 0 0;
}

.u-section-4 .u-container-layout-4 {
  padding: 10px;
}

.u-section-4 .u-text-2 {
  font-weight: 700;
  font-style: normal;
  margin: 11px 0 0;
}

.u-section-4 .u-container-layout-5 {
  padding: 10px 10px 9px;
}

.u-section-4 .u-text-3 {
  font-weight: 700;
  font-style: normal;
  margin: 11px 0 0;
}

.u-section-4 .u-container-layout-6 {
  padding: 10px 10px 9px;
}

.u-section-4 .u-text-4 {
  font-weight: 700;
  font-style: normal;
  margin: 11px 0 0;
}

.u-section-4 .u-text-5 {
  font-style: normal;
  margin: 30px 0 0;
}

.u-section-4 .u-text-6 {
  font-style: normal;
  margin: 20px 0 0;
}

.u-section-4 .u-text-7 {
  font-style: normal;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 730px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 704px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-right: 0;
  }

  .u-section-4 .u-image-1 {
    height: 644px;
    --radius: 10px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 747px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 161px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 587px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 985px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-top: 0;
  }

  .u-section-4 .u-image-1 {
    height: 955px;
    object-position: 55.03% 76.54%;
    margin-top: 0;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-4 .u-container-layout-2 {
    padding: 0;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 168px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 1463px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 600px;
  }

  .u-section-4 .u-image-1 {
    height: 716px;
  }

  .u-section-4 .u-list-1 {
    margin-top: -30px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 1467px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 481px;
  }

  .u-section-4 .u-image-1 {
    height: 451px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 212px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-bottom: 9px;
  }
} .u-section-5 {
  background-image: none;
}

.u-section-5 .u-sheet-1 {
  min-height: 50vw;
}

.u-section-5 .u-text-1 {
  text-transform: uppercase;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 51px auto 0;
}

.u-section-5 .u-layout-wrap-1 {
  pointer-events: auto;
  width: 1140px;
  margin: 40px 0 60px;
}

.u-section-5 .u-layout-cell-1 {
  min-height: 590px;
  pointer-events: auto;
  background-image: none;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-5 .u-container-layout-1 {
  padding: 0 20px 30px 50px;
}

.u-section-5 .u-image-1 {
  height: 682px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: -92px auto 0;
}

.u-section-5 .u-layout-cell-2 {
  min-height: 597px;
  pointer-events: auto;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-5 .u-container-layout-2 {
  padding: 30px 50px;
}

.u-section-5 .u-list-1 {
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 375px;
  grid-gap: 10px;
}

.u-section-5 .u-container-layout-3 {
  padding: 10px;
}

.u-section-5 .u-icon-1 {
  width: 64px;
  background-image: none;
  height: 64px;
  margin: 40px auto 0;
  padding: 13px;
}

.u-section-5 .u-text-2 {
  font-weight: 700;
  text-transform: none;
  font-size: 1.25rem;
  margin: 20px auto 0;
}

.u-section-5 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-5 .u-container-layout-4 {
  padding: 10px;
}

.u-section-5 .u-icon-2 {
  width: 64px;
  background-image: none;
  height: 64px;
  margin: 40px auto 0;
  padding: 13px;
}

.u-section-5 .u-text-4 {
  font-weight: 700;
  text-transform: none;
  font-size: 1.25rem;
  margin: 20px auto 0;
}

.u-section-5 .u-text-5 {
  margin: 20px 0 0;
}

.u-section-5 .u-container-layout-5 {
  padding: 10px;
}

.u-section-5 .u-icon-3 {
  width: 64px;
  background-image: none;
  height: 64px;
  margin: 40px auto 0;
  padding: 13px;
}

.u-section-5 .u-text-6 {
  font-weight: 700;
  text-transform: none;
  font-size: 1.25rem;
  margin: 20px auto 0;
}

.u-section-5 .u-text-7 {
  margin: 20px 0 0;
}

.u-section-5 .u-container-layout-6 {
  padding: 10px;
}

.u-section-5 .u-icon-4 {
  width: 64px;
  background-image: none;
  height: 64px;
  margin: 40px auto 0;
  padding: 13px;
}

.u-section-5 .u-text-8 {
  font-weight: 700;
  text-transform: none;
  font-size: 1.25rem;
  margin: 20px auto 0;
}

.u-section-5 .u-text-9 {
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-5 .u-layout-wrap-1 {
    width: 940px;
    margin-top: 90px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 809px;
  }

  .u-section-5 .u-image-1 {
    height: 599px;
    margin-top: 105px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 492px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 40px;
    padding-right: 40px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 296px;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 588px;
  }

  .u-section-5 .u-layout-wrap-1 {
    width: 720px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 536px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 30px;
  }

  .u-section-5 .u-image-1 {
    height: 551px;
    object-position: 50% 41.98%;
    margin-top: -45px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 628px;
  }

  .u-section-5 .u-container-layout-2 {
    padding: 0 30px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 611px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1331px;
  }

  .u-section-5 .u-text-1 {
    width: auto;
    margin-top: 30px;
  }

  .u-section-5 .u-layout-wrap-1 {
    width: 540px;
    margin-top: 60px;
    margin-bottom: 30px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 376px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-right: 30px;
  }

  .u-section-5 .u-image-1 {
    height: 576px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-5 .u-text-2 {
    font-size: 1rem;
  }

  .u-section-5 .u-text-4 {
    font-size: 1rem;
  }

  .u-section-5 .u-text-6 {
    font-size: 1rem;
  }

  .u-section-5 .u-text-8 {
    font-size: 1rem;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1262px;
  }

  .u-section-5 .u-text-1 {
    margin-top: 23px;
  }

  .u-section-5 .u-layout-wrap-1 {
    width: 340px;
    margin-top: 67px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 237px;
  }

  .u-section-5 .u-image-1 {
    height: 384px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 740px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-5 .u-list-1 {
    width: 310px;
    margin-left: auto;
    margin-right: auto;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 739px;
  }
}.u-section-6 .u-sheet-1 {
  min-height: 211px;
}

.u-section-6 .u-text-1 {
  text-transform: uppercase;
  margin: 20px auto 0;
}

.u-section-6 .u-list-1 {
  margin-top: 30px;
  margin-bottom: 20px;
}

.u-section-6 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 88px;
  grid-gap: 10px;
}

.u-section-6 .u-container-layout-1 {
  padding: 10px 10px 0;
}

.u-section-6 .u-text-2 {
  font-weight: 700;
  margin: 0;
}

.u-section-6 .u-container-layout-2 {
  padding: 10px 10px 0;
}

.u-section-6 .u-text-3 {
  font-weight: 700;
  margin: 0;
}

.u-section-6 .u-container-layout-3 {
  padding: 10px 10px 0;
}

.u-section-6 .u-text-4 {
  font-weight: 700;
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-6 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 72px;
  }
}

@media (max-width: 991px) {
  .u-section-6 .u-sheet-1 {
    min-height: 352px;
  }

  .u-section-6 .u-text-1 {
    width: auto;
    margin-right: 79px;
    margin-left: 0;
  }

  .u-section-6 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 177px;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-sheet-1 {
    min-height: 336px;
  }

  .u-section-6 .u-text-1 {
    margin-left: 33px;
    margin-right: 33px;
  }

  .u-section-6 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-6 .u-text-1 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-6 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-6 .u-text-2 {
    margin-right: auto;
  }

  .u-section-6 .u-text-3 {
    margin-right: auto;
  }

  .u-section-6 .u-text-4 {
    margin-right: auto;
  }
}.u-section-7 .u-sheet-1 {
  min-height: 599px;
}

.u-section-7 .u-text-1 {
  margin-bottom: 0;
  margin-right: auto;
  margin-left: auto;
  text-transform: uppercase;
}

.u-section-7 .u-table-1 {
  width: 822px;
  margin: 30px auto 60px;
}

.u-block-d841-5 {
  font-weight: 700;
}

.u-section-7 .u-table-header-1 {
  font-weight: 700;
}

.u-section-7 .u-table-cell-1 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-2 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-3 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-4 {
  font-weight: 700;
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-5 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-6 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-7 {
  font-weight: 700;
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-8 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-9 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-10 {
  font-weight: 700;
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-11 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-12 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-13 {
  font-weight: 700;
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-14 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-15 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-16 {
  font-weight: 700;
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-17 {
  font-size: 1.125rem;
}

.u-section-7 .u-table-cell-18 {
  font-size: 1.125rem;
}

.u-block-d841-31 {
  height: 47px;
}

@media (max-width: 991px) {
  .u-section-7 .u-table-1 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-sheet-1 {
    min-height: 551px;
  }

  .u-section-7 .u-table-1 {
    width: 540px;
    margin-bottom: 29px;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-table-1 {
    width: 340px;
  }
} .u-section-8 {
  background-image: none;
}

.u-section-8 .u-sheet-1 {
  min-height: 570px;
}

.u-section-8 .u-text-1 {
  text-transform: uppercase;
  margin: 20px auto 0;
}

.u-section-8 .u-blog-1 {
  margin-top: 12px;
  margin-bottom: 35px;
}

.u-section-8 .u-repeater-1 {
  grid-auto-columns: 365.328px 365.328px 365.328px;
  grid-template-columns: repeat(3, calc(33.3333% - 14.6667px));
  min-height: 450px;
  grid-gap: 22px;
}

.u-section-8 .u-repeater-item-1 {
  background-image: none;
}

.u-section-8 .u-container-layout-1 {
  padding: 30px;
}

.u-section-8 .u-image-1 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-2 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-3 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-1 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-container-layout-2 {
  padding: 30px;
}

.u-section-8 .u-image-2 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-4 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-5 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-2 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-container-layout-3 {
  padding: 30px;
}

.u-section-8 .u-image-3 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-6 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-7 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-3 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-container-layout-4 {
  padding: 30px;
}

.u-section-8 .u-image-4 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-8 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-9 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-4 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-container-layout-5 {
  padding: 30px;
}

.u-section-8 .u-image-5 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-10 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-11 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-5 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-container-layout-6 {
  padding: 30px;
}

.u-section-8 .u-image-6 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-8 .u-text-12 {
  text-transform: uppercase;
  margin-top: 17px;
  margin-bottom: 0;
}

.u-section-8 .u-text-13 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-8 .u-btn-6 {
  background-image: none;
  border-style: solid;
  margin: 17px auto 0 0;
  padding: 0;
}

.u-section-8 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
}

.u-section-8 .u-gallery-nav-2 {
  position: absolute;
  right: 10px;
  width: 40px;
  height: 40px;
}

@media (max-width: 1199px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 14.6667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 14.6667px));
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(50% - 11.000025px);
    grid-template-columns: repeat(2, calc(50% - 11.000025px));
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-8 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-8 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-8 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-8 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-8 .u-container-layout-5 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-8 .u-container-layout-6 {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-8 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}