.u-section-1 .u-sheet-1 {
  min-height: 718px;
}

.u-section-1 .u-image-1 {
  height: 634px;
  width: 967px;
  object-position: 50% 7.71%;
  --radius: 10px;
  margin: 10px auto 0;
}

.u-section-1 .u-text-1 {
  text-transform: uppercase;
  font-size: 1.875rem;
  margin: -1px 0 0;
}

.u-section-1 .u-text-2 {
  font-style: italic;
  margin: 0 0 10px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 700px;
  }

  .u-section-1 .u-image-1 {
    height: 616px;
    width: 940px;
  }

  .u-section-1 .u-text-1 {
    --animation-custom_in-translate_x: 300px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
    margin-top: 17px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 556px;
  }

  .u-section-1 .u-image-1 {
    height: 472px;
    width: 720px;
  }

  .u-section-1 .u-text-1 {
    width: auto;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 438px;
  }

  .u-section-1 .u-image-1 {
    width: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 425px;
  }

  .u-section-1 .u-image-1 {
    height: 343px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-text-1 {
    margin-top: 0;
  }

  .u-section-1 .u-text-2 {
    width: auto;
  }
}.u-section-2 .u-sheet-1 {
  min-height: 201px;
}

.u-section-2 .u-text-1 {
  text-transform: uppercase;
  font-size: 2.25rem;
  margin: 22px auto 0;
}

.u-section-2 .u-list-1 {
  margin-top: 0;
  margin-bottom: 22px;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 118px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-1 {
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-2 .u-container-layout-1 {
  padding: 10px;
}

.u-section-2 .u-text-2 {
  font-weight: 700;
  text-decoration: line-through !important;
  margin: 8px 14px 0 371px;
}

.u-section-2 .u-btn-1 {
  --radius: 50px;
  font-weight: 700;
  font-size: 0.875rem;
  letter-spacing: 1px;
  font-style: normal;
  margin: -27px auto 0 14px;
  padding: 3px 25px 4px 23px;
}

.u-section-2 .u-text-3 {
  font-size: 1.875rem;
  font-weight: 800;
  margin: 0;
}

.u-section-2 .u-list-item-2 {
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-2 .u-container-layout-2 {
  padding: 10px;
}

.u-section-2 .u-text-4 {
  font-weight: 700;
  text-decoration: line-through !important;
  margin: 8px 14px 0 371px;
}

.u-section-2 .u-btn-2 {
  --radius: 50px;
  font-weight: 700;
  font-size: 0.875rem;
  letter-spacing: 1px;
  font-style: normal;
  margin: -27px auto 0 14px;
  padding: 3px 25px 4px 23px;
}

.u-section-2 .u-text-5 {
  font-size: 1.875rem;
  font-weight: 800;
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-text-1 {
    --animation-custom_in-translate_x: 300px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-2 .u-list-1 {
    margin-top: 61px;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 97px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
    margin-left: 185px;
  }

  .u-section-2 .u-btn-1 {
    margin-left: 0;
  }

  .u-section-2 .u-text-4 {
    margin-right: 0;
    margin-left: 185px;
  }

  .u-section-2 .u-btn-2 {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-list-1 {
    margin-top: 20px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-2 .u-text-2 {
    width: auto;
    margin-top: 10px;
    margin-right: -167px;
    margin-left: 187px;
  }

  .u-section-2 .u-btn-1 {
    margin-top: -29px;
    margin-left: 125px;
  }

  .u-section-2 .u-text-3 {
    margin-left: 10px;
    margin-right: 10px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-2 .u-text-4 {
    width: auto;
    margin-top: 10px;
    margin-right: -167px;
    margin-left: 187px;
  }

  .u-section-2 .u-btn-2 {
    margin-top: -29px;
    margin-left: 125px;
  }

  .u-section-2 .u-text-5 {
    margin-left: 10px;
    margin-right: 10px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 258px;
  }

  .u-section-2 .u-list-1 {
    margin-top: 0;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-text-2 {
    margin-right: 64px;
    margin-left: 214px;
  }

  .u-section-2 .u-btn-1 {
    margin-left: 0;
  }

  .u-section-2 .u-text-3 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-text-4 {
    margin-right: 64px;
    margin-left: 214px;
  }

  .u-section-2 .u-btn-2 {
    margin-left: 0;
  }

  .u-section-2 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 257px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.875rem;
    margin-top: 24px;
  }

  .u-section-2 .u-list-1 {
    margin-top: 10px;
    margin-bottom: 24px;
  }

  .u-section-2 .u-repeater-1 {
    grid-template-columns: repeat(1, 100%);
    min-height: 167px;
    grid-gap: 0px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-bottom: 12px;
  }

  .u-section-2 .u-text-2 {
    margin-top: 9px;
    margin-right: 24px;
    margin-left: 201px;
  }

  .u-section-2 .u-btn-1 {
    margin-top: -27px;
  }

  .u-section-2 .u-text-3 {
    font-weight: 700;
    width: auto;
    font-size: 1.25rem;
  }

  .u-section-2 .u-container-layout-2 {
    padding-bottom: 12px;
  }

  .u-section-2 .u-text-4 {
    margin-top: 9px;
    margin-right: 24px;
    margin-left: 201px;
  }

  .u-section-2 .u-btn-2 {
    margin-top: -27px;
  }

  .u-section-2 .u-text-5 {
    font-weight: 700;
    width: auto;
    font-size: 1.25rem;
  }
}.u-section-3 .u-sheet-1 {
  min-height: 1125px;
}

.u-section-3 .u-text-1 {
  font-size: 2.25rem;
  font-weight: 700;
  text-transform: uppercase;
  margin: 60px auto 0;
}

.u-section-3 .u-form-1 {
  height: 850px;
  --thumb-color: #6a5e41;
  --thumb-hover-color: #f0e3d1;
  --track-color: #6a5e41;
  --track-active-color: #f0e3d1;
  margin: 30px 287px 60px 283px;
}

.u-section-3 .u-form-group-1 {
  margin-top: 20px !important;
  margin-bottom: 0;
}

.u-section-3 .u-input-1 {
  --radius: 10px;
}

.u-section-3 .u-form-group-2 {
  margin-left: 0;
  margin-top: 20px !important;
  margin-bottom: 0;
}

.u-section-3 .u-input-2 {
  --radius: 10px;
}

.u-section-3 .u-form-group-3 {
  margin-bottom: 0;
  margin-top: 20px !important;
}

.u-section-3 .u-input-3 {
  --radius: 10px;
}

.u-section-3 .u-form-group-4 {
  margin-bottom: 0;
  margin-top: 20px !important;
}

.u-section-3 .u-label-4 {
  --radius: 10px;
}

.u-section-3 .u-input-4 {
  --radius: 10px;
}

.u-section-3 .u-form-group-5 {
  margin-top: 20px !important;
  margin-bottom: 0;
}

.u-section-3 .u-input-5 {
  --radius: 10px;
}

.u-section-3 .u-form-group-6 {
  margin-top: 20px !important;
}

.u-section-3 .u-input-6 {
  --radius: 10px;
}

.u-section-3 .u-form-group-7 {
  margin-bottom: 0;
  margin-top: 20px;
}

.u-section-3 .u-input-7 {
  --radius: 10px;
}

.u-section-3 .u-form-group-8 {
  --progress: 0%;
  margin-bottom: 0;
  margin-top: 20px !important;
}

.u-section-3 .u-label-8 {
  --radius: 10px;
}

.u-section-3 .u-input-8 {
  --radius: 10px;
}

.u-section-3 .u-form-group-9 {
  margin-left: 0;
  margin-top: 10px !important;
}

.u-section-3 .u-form-group-10 {
  margin-top: 20px !important;
}

.u-section-3 .u-btn-1 {
  --radius: 6px;
  font-size: 0.9375rem;
  font-weight: 700;
  padding: 10px 57px 10px 56px;
}

@media (max-width: 1199px) {
  .u-section-3 .u-form-1 {
    width: 570px;
    margin-left: 183px;
    margin-right: 187px;
  }

  .u-section-3 .u-btn-1 {
    --radius: 20px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-form-1 {
    margin-top: 20px;
    margin-right: 77px;
    margin-left: 73px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-text-1 {
    width: auto;
    margin-top: 30px;
  }

  .u-section-3 .u-form-1 {
    height: 851px;
    margin-top: 0;
    margin-bottom: 40px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-text-1 {
    font-size: 1.5rem;
    margin-top: 21px;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-form-1 {
    margin-bottom: 60px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }
}.disable-btn {
	pointer-events: none;
}

/* Message Popup Stilovi */
.message-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.message-popup-overlay.show {
    opacity: 1;
    visibility: visible;
}

.message-popup-container {
    background: white;
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.message-popup-overlay.show .message-popup-container {
    transform: scale(1);
}

.message-popup-content {
    padding: 30px 20px;
    text-align: center;
}

.popup-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    font-weight: bold;
}

.popup-icon.success {
    background: #d4edda;
    color: #155724;
}

.popup-icon.success::before {
    content: "✓";
}

.popup-icon.error {
    background: #f8d7da;
    color: #721c24;
}

.popup-icon.error::before {
    content: "✕";
}

.popup-title {
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
}

.popup-title.success {
    color: #155724;
}

.popup-title.error {
    color: #721c24;
}

.popup-message {
    margin: 0 0 25px 0;
    font-size: 16px;
    line-height: 1.5;
    color: #666;
}

.popup-ok-btn {
    background-color: #d9a832;
    color: #4e3629;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    min-width: 100px;
}

.popup-ok-btn:hover {
    background-color: #cb9a26;
    color: #4e3629;
}

@media (max-width: 480px) {
    .message-popup-content {
        padding: 25px 15px;
    }
    
    .popup-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .popup-title {
        font-size: 18px;
    }
    
    .popup-message {
        font-size: 14px;
    }
}
